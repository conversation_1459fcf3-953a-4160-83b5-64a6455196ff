using Microsoft.AspNetCore.Mvc;
using ProductionManagementSystem.Services;

namespace ProductionManagementSystem.Controllers.Api
{
    [Route("api/calculate")]
    [ApiController]
    public class CalculationApiController : ControllerBase
    {
        private readonly IProductionCalculationService _calculationService;

        public CalculationApiController(IProductionCalculationService calculationService)
        {
            _calculationService = calculationService;
        }

        // POST: api/calculate/production
        [HttpPost("production")]
        public async Task<ActionResult<object>> CalculateProduction([FromBody] ProductionCalculationRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                // Рассчитываем время производства
                var productionTime = _calculationService.CalculateProductionTime(
                    request.ProductId, 
                    request.Quantity, 
                    request.EfficiencyFactor);

                // Рассчитываем дату окончания
                var estimatedEndDate = _calculationService.CalculateEstimatedEndDate(
                    request.StartDate, 
                    productionTime);

                // Проверяем наличие материалов
                var hasSufficientMaterials = await _calculationService
                    .CheckMaterialAvailabilityAsync(request.ProductId, request.Quantity);

                // Получаем требования к материалам
                var materialRequirements = await _calculationService
                    .GetMaterialRequirementsAsync(request.ProductId, request.Quantity);

                // Находим доступную производственную линию
                var availableProductionLine = await _calculationService
                    .FindAvailableProductionLineAsync();

                var result = new
                {
                    ProductionTimeMinutes = productionTime,
                    ProductionTimeHours = Math.Round(productionTime / 60.0, 2),
                    EstimatedEndDate = estimatedEndDate,
                    HasSufficientMaterials = hasSufficientMaterials,
                    MaterialRequirements = materialRequirements,
                    AvailableProductionLine = availableProductionLine != null ? new
                    {
                        availableProductionLine.Id,
                        availableProductionLine.Name,
                        availableProductionLine.EfficiencyFactor
                    } : null,
                    Recommendations = GenerateRecommendations(
                        hasSufficientMaterials, 
                        availableProductionLine != null, 
                        materialRequirements)
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"Ошибка при расчете: {ex.Message}");
            }
        }

        // POST: api/calculate/materials
        [HttpPost("materials")]
        public async Task<ActionResult<object>> CalculateMaterialRequirements([FromBody] MaterialCalculationRequest request)
        {
            try
            {
                var materialRequirements = await _calculationService
                    .GetMaterialRequirementsAsync(request.ProductId, request.Quantity);

                var totalCost = materialRequirements.Sum(mr => mr.RequiredQuantity * 100); // Примерная стоимость
                var missingMaterials = materialRequirements.Where(mr => !mr.IsSufficient).ToList();

                var result = new
                {
                    MaterialRequirements = materialRequirements,
                    TotalEstimatedCost = totalCost,
                    HasMissingMaterials = missingMaterials.Any(),
                    MissingMaterials = missingMaterials,
                    CanProduce = !missingMaterials.Any()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"Ошибка при расчете материалов: {ex.Message}");
            }
        }

        // POST: api/calculate/capacity
        [HttpPost("capacity")]
        public async Task<ActionResult<object>> CalculateProductionCapacity([FromBody] CapacityCalculationRequest request)
        {
            try
            {
                var availableLines = await _calculationService.FindAvailableProductionLineAsync();
                
                // Простой расчет производственной мощности
                var dailyCapacity = 0;
                var weeklyCapacity = 0;
                var monthlyCapacity = 0;

                if (availableLines != null)
                {
                    var productionTimePerUnit = _calculationService.CalculateProductionTime(
                        request.ProductId, 1, availableLines.EfficiencyFactor);
                    
                    var unitsPerDay = (8 * 60) / productionTimePerUnit; // 8 рабочих часов
                    dailyCapacity = Math.Max(0, unitsPerDay);
                    weeklyCapacity = dailyCapacity * 5; // 5 рабочих дней
                    monthlyCapacity = weeklyCapacity * 4; // 4 недели
                }

                var result = new
                {
                    ProductId = request.ProductId,
                    DailyCapacity = dailyCapacity,
                    WeeklyCapacity = weeklyCapacity,
                    MonthlyCapacity = monthlyCapacity,
                    HasAvailableCapacity = availableLines != null,
                    RecommendedBatchSize = Math.Min(dailyCapacity, 100) // Рекомендуемый размер партии
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"Ошибка при расчете мощности: {ex.Message}");
            }
        }

        private List<string> GenerateRecommendations(
            bool hasSufficientMaterials, 
            bool hasAvailableProductionLine, 
            List<Models.ViewModels.MaterialRequirement> materialRequirements)
        {
            var recommendations = new List<string>();

            if (!hasSufficientMaterials)
            {
                recommendations.Add("Необходимо пополнить запасы материалов перед началом производства");
                
                var missingMaterials = materialRequirements.Where(mr => !mr.IsSufficient);
                foreach (var material in missingMaterials)
                {
                    var shortage = material.RequiredQuantity - material.AvailableQuantity;
                    recommendations.Add($"Недостает {shortage} {material.UnitOfMeasure} материала '{material.MaterialName}'");
                }
            }

            if (!hasAvailableProductionLine)
            {
                recommendations.Add("Нет доступных производственных линий. Рассмотрите возможность переноса даты начала производства");
            }

            if (hasSufficientMaterials && hasAvailableProductionLine)
            {
                recommendations.Add("Все условия для начала производства выполнены");
            }

            return recommendations;
        }
    }

    public class ProductionCalculationRequest
    {
        public int ProductId { get; set; }
        public int Quantity { get; set; }
        public DateTime StartDate { get; set; }
        public float EfficiencyFactor { get; set; } = 1.0f;
    }

    public class MaterialCalculationRequest
    {
        public int ProductId { get; set; }
        public int Quantity { get; set; }
    }

    public class CapacityCalculationRequest
    {
        public int ProductId { get; set; }
    }
}
