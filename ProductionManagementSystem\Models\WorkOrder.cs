using System.ComponentModel.DataAnnotations;

namespace ProductionManagementSystem.Models
{
    public class WorkOrder
    {
        public int Id { get; set; }

        [Required]
        public int ProductId { get; set; }

        public int? ProductionLineId { get; set; }

        [Range(1, int.MaxValue)]
        public int Quantity { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EstimatedEndDate { get; set; }

        public WorkOrderStatus Status { get; set; } = WorkOrderStatus.Pending;

        public DateTime? ActualStartDate { get; set; }
        public DateTime? ActualEndDate { get; set; }
        public int Progress { get; set; } = 0; // Процент выполнения 0-100

        // Navigation properties
        public virtual Product Product { get; set; } = null!;
        public virtual ProductionLine? ProductionLine { get; set; }

        // Computed properties
        public bool IsOverdue => Status != WorkOrderStatus.Completed &&
                                Status != WorkOrderStatus.Cancelled &&
                                DateTime.Now > EstimatedEndDate;

        public TimeSpan EstimatedDuration => EstimatedEndDate - StartDate;
    }
}
