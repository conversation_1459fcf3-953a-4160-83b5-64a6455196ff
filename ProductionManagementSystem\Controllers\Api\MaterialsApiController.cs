using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;

namespace ProductionManagementSystem.Controllers.Api
{
    [Route("api/materials")]
    [ApiController]
    public class MaterialsApiController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public MaterialsApiController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/materials?lowStock=true
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Material>>> GetMaterials(bool? lowStock = null)
        {
            var query = _context.Materials.AsQueryable();

            if (lowStock == true)
            {
                query = query.Where(m => m.Quantity <= m.MinimalStock);
            }

            var materials = await query.OrderBy(m => m.Name).ToListAsync();
            return Ok(materials);
        }

        // GET: api/materials/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Material>> GetMaterial(int id)
        {
            var material = await _context.Materials.FindAsync(id);

            if (material == null)
            {
                return NotFound();
            }

            return material;
        }

        // POST: api/materials
        [HttpPost]
        public async Task<ActionResult<Material>> CreateMaterial(Material material)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _context.Materials.Add(material);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetMaterial), new { id = material.Id }, material);
        }

        // PUT: api/materials/5/stock
        [HttpPut("{id}/stock")]
        public async Task<IActionResult> UpdateStock(int id, [FromBody] UpdateStockRequest request)
        {
            var material = await _context.Materials.FindAsync(id);
            if (material == null)
            {
                return NotFound();
            }

            if (request.Quantity < 0)
            {
                return BadRequest("Количество не может быть отрицательным");
            }

            material.Quantity = request.Quantity;
            await _context.SaveChangesAsync();

            return Ok(new { material.Id, material.Name, material.Quantity, IsLowStock = material.IsLowStock });
        }

        // PUT: api/materials/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateMaterial(int id, Material material)
        {
            if (id != material.Id)
            {
                return BadRequest();
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _context.Entry(material).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MaterialExists(id))
                {
                    return NotFound();
                }
                throw;
            }

            return NoContent();
        }

        // DELETE: api/materials/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteMaterial(int id)
        {
            var material = await _context.Materials.FindAsync(id);
            if (material == null)
            {
                return NotFound();
            }

            // Проверяем, используется ли материал в продуктах
            var isUsed = await _context.ProductMaterials.AnyAsync(pm => pm.MaterialId == id);
            if (isUsed)
            {
                return BadRequest("Нельзя удалить материал, который используется в продуктах");
            }

            _context.Materials.Remove(material);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool MaterialExists(int id)
        {
            return _context.Materials.Any(e => e.Id == id);
        }
    }

    public class UpdateStockRequest
    {
        public decimal Quantity { get; set; }
    }
}
