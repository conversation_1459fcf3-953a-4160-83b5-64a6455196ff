using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;
using ProductionManagementSystem.Models.ViewModels;

namespace ProductionManagementSystem.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<IActionResult> Index()
    {
        var dashboard = new DashboardViewModel
        {
            TotalProducts = await _context.Products.CountAsync(),
            TotalMaterials = await _context.Materials.CountAsync(),
            ActiveProductionLines = await _context.ProductionLines
                .CountAsync(pl => pl.Status == ProductionLineStatus.Active),
            PendingWorkOrders = await _context.WorkOrders
                .CountAsync(wo => wo.Status == WorkOrderStatus.Pending),
            InProgressWorkOrders = await _context.WorkOrders
                .CountAsync(wo => wo.Status == WorkOrderStatus.InProgress),
            OverdueWorkOrders = await _context.WorkOrders
                .CountAsync(wo => wo.Status != WorkOrderStatus.Completed &&
                                 wo.Status != WorkOrderStatus.Cancelled &&
                                 wo.EstimatedEndDate < DateTime.Now),
            LowStockMaterials = await _context.Materials
                .CountAsync(m => m.Quantity <= m.MinimalStock)
        };

        // Получаем последние заказы
        dashboard.RecentWorkOrders = await _context.WorkOrders
            .Include(wo => wo.Product)
            .Include(wo => wo.ProductionLine)
            .OrderByDescending(wo => wo.Id)
            .Take(5)
            .ToListAsync();

        // Получаем материалы с низким запасом
        dashboard.LowStockMaterialsList = await _context.Materials
            .Where(m => m.Quantity <= m.MinimalStock)
            .Take(5)
            .ToListAsync();

        // Получаем производственные линии
        dashboard.ProductionLines = await _context.ProductionLines
            .Include(pl => pl.CurrentWorkOrder)
            .ThenInclude(wo => wo!.Product)
            .ToListAsync();

        // Статистика производства
        var today = DateTime.Today;
        var weekStart = today.AddDays(-(int)today.DayOfWeek);
        var monthStart = new DateTime(today.Year, today.Month, 1);

        dashboard.TotalCompletedToday = await _context.WorkOrders
            .CountAsync(wo => wo.Status == WorkOrderStatus.Completed &&
                             wo.ActualEndDate.HasValue &&
                             wo.ActualEndDate.Value.Date == today);

        dashboard.TotalCompletedThisWeek = await _context.WorkOrders
            .CountAsync(wo => wo.Status == WorkOrderStatus.Completed &&
                             wo.ActualEndDate.HasValue &&
                             wo.ActualEndDate.Value >= weekStart);

        dashboard.TotalCompletedThisMonth = await _context.WorkOrders
            .CountAsync(wo => wo.Status == WorkOrderStatus.Completed &&
                             wo.ActualEndDate.HasValue &&
                             wo.ActualEndDate.Value >= monthStart);

        return View(dashboard);
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
