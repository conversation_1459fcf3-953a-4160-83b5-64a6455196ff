﻿<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Система управления производством</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/ProductionManagementSystem.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
            <div class="container-fluid">
                <a class="navbar-brand fw-bold" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-gear-fill me-2"></i>Система управления производством
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="bi bi-house-door me-1"></i>Главная
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Products" asp-action="Index">
                                <i class="bi bi-box-seam me-1"></i>Продукты
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Materials" asp-action="Index">
                                <i class="bi bi-archive me-1"></i>Материалы
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="WorkOrders" asp-action="Index">
                                <i class="bi bi-clipboard-check me-1"></i>Заказы
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ProductionLines" asp-action="Index">
                                <i class="bi bi-diagram-3 me-1"></i>Производство
                            </a>
                        </li>

                        <!-- Дополнительные действия в dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-plus-circle me-1"></i>Создать
                                <i class="bi bi-chevron-down ms-1"></i>
                            </a>
                            <ul class="dropdown-menu">
                                <li><h6 class="dropdown-header">Новые элементы</h6></li>
                                <li><a class="dropdown-item" asp-controller="Products" asp-action="Create">
                                    <i class="bi bi-box-seam me-2"></i>Создать продукт
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Materials" asp-action="Create">
                                    <i class="bi bi-archive me-2"></i>Добавить материал
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="WorkOrders" asp-action="Create">
                                    <i class="bi bi-clipboard-check me-2"></i>Создать заказ
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="ProductionLines" asp-action="Create">
                                    <i class="bi bi-diagram-3 me-2"></i>Добавить линию
                                </a></li>
                            </ul>
                        </li>

                        <!-- Быстрые фильтры -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-funnel me-1"></i>Фильтры
                                <i class="bi bi-chevron-down ms-1"></i>
                            </a>
                            <ul class="dropdown-menu">
                                <li><h6 class="dropdown-header">Быстрые фильтры</h6></li>
                                <li><a class="dropdown-item" asp-controller="Materials" asp-action="Index" asp-route-lowStock="true">
                                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>Низкий запас материалов
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="WorkOrders" asp-action="Index" asp-route-status="Pending">
                                    <i class="bi bi-clock text-warning me-2"></i>Ожидающие заказы
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="WorkOrders" asp-action="Index" asp-route-status="InProgress">
                                    <i class="bi bi-play-circle text-info me-2"></i>Заказы в работе
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="ProductionLines" asp-action="Schedule">
                                    <i class="bi bi-calendar3 text-primary me-2"></i>Расписание производства
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container-fluid">
        <!-- Уведомления -->
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                <i class="bi bi-check-circle me-2"></i>@TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        }
        @if (TempData["InfoMessage"] != null)
        {
            <div class="alert alert-info alert-dismissible fade show mt-3" role="alert">
                <i class="bi bi-info-circle me-2"></i>@TempData["InfoMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        }

        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - ProductionManagementSystem - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <!-- Скрипт для улучшения навигации -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Подсветка активного пункта меню
            const currentPath = window.location.pathname.toLowerCase();
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href && currentPath.includes(href.toLowerCase()) && href !== '/') {
                    link.closest('.nav-item').classList.add('active');
                } else if (href === '/' && currentPath === '/') {
                    link.closest('.nav-item').classList.add('active');
                }
            });

            // Улучшенное поведение dropdown на десктопе
            const dropdowns = document.querySelectorAll('.navbar-nav .dropdown');

            dropdowns.forEach(dropdown => {
                const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
                const dropdownMenu = dropdown.querySelector('.dropdown-menu');

                // Показываем dropdown при наведении на десктопе
                if (window.innerWidth > 991) {
                    dropdown.addEventListener('mouseenter', function() {
                        dropdownMenu.classList.add('show');
                        dropdownToggle.setAttribute('aria-expanded', 'true');
                    });

                    dropdown.addEventListener('mouseleave', function() {
                        dropdownMenu.classList.remove('show');
                        dropdownToggle.setAttribute('aria-expanded', 'false');
                    });
                }
            });

            // Закрытие мобильного меню при клике на ссылку
            const navbarCollapse = document.querySelector('.navbar-collapse');
            const navLinks2 = document.querySelectorAll('.navbar-nav .nav-link');

            navLinks2.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 991) {
                        const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                            toggle: false
                        });
                        bsCollapse.hide();
                    }
                });
            });
        });

        // Обновление при изменении размера окна
        window.addEventListener('resize', function() {
            const dropdowns = document.querySelectorAll('.navbar-nav .dropdown');

            dropdowns.forEach(dropdown => {
                const dropdownMenu = dropdown.querySelector('.dropdown-menu');
                if (window.innerWidth <= 991) {
                    dropdownMenu.classList.remove('show');
                }
            });
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
