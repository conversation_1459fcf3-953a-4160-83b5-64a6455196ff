using System.ComponentModel.DataAnnotations;

namespace ProductionManagementSystem.Models
{
    public class ProductionLine
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        public ProductionLineStatus Status { get; set; } = ProductionLineStatus.Stopped;

        [Range(0.5, 2.0)]
        public float EfficiencyFactor { get; set; } = 1.0f;

        public int? CurrentWorkOrderId { get; set; }

        // Navigation properties
        public virtual WorkOrder? CurrentWorkOrder { get; set; }
        public virtual ICollection<WorkOrder> WorkOrders { get; set; } = new List<WorkOrder>();

        // Computed property
        public bool IsAvailable => Status == ProductionLineStatus.Active && CurrentWorkOrderId == null;
    }
}
