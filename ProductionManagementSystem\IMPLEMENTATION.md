# Реализация системы управления производством

## Обзор реализации

Создана полнофункциональная веб-система управления производством на ASP.NET Core MVC согласно техническому заданию.

## Архитектура и технологии

### Использованные технологии
- **ASP.NET Core 9.0 MVC** - основной фреймворк
- **Entity Framework Core 9.0** - ORM для работы с базой данных
- **SQL Server LocalDB** - база данных (с возможностью переключения на SQLite)
- **Bootstrap 5** - CSS фреймворк для UI
- **Bootstrap Icons** - иконки
- **JavaScript/jQuery** - интерактивность на клиенте
- **DataTables** - расширенные возможности таблиц

### Архитектурные паттерны
- **MVC (Model-View-Controller)** - основной паттерн
- **Repository Pattern** - через Entity Framework
- **Service Layer** - бизнес-логика в сервисах
- **Dependency Injection** - встроенный DI контейнер ASP.NET Core

## Структура базы данных

### Реализованные модели

1. **Product** - Продукты
   - Id, Name, Description, Specifications (JSON)
   - Category, MinimalStock, ProductionTimePerUnit
   - Связи: ProductMaterials, WorkOrders

2. **Material** - Материалы
   - Id, Name, Quantity, UnitOfMeasure, MinimalStock
   - Вычисляемое свойство: IsLowStock
   - Связи: ProductMaterials

3. **ProductionLine** - Производственные линии
   - Id, Name, Status, EfficiencyFactor, CurrentWorkOrderId
   - Вычисляемое свойство: IsAvailable
   - Связи: WorkOrders, CurrentWorkOrder

4. **WorkOrder** - Производственные заказы
   - Id, ProductId, ProductionLineId, Quantity
   - StartDate, EstimatedEndDate, Status, Progress
   - ActualStartDate, ActualEndDate
   - Вычисляемые свойства: IsOverdue, EstimatedDuration

5. **ProductMaterial** - Связь продукт-материал
   - ProductId, MaterialId, QuantityNeeded
   - Составной первичный ключ

### Перечисления
- **ProductionLineStatus**: Active, Stopped
- **WorkOrderStatus**: Pending, InProgress, Completed, Cancelled

## API Контроллеры

### MaterialsApiController
- `GET /api/materials` - список материалов с фильтрацией
- `POST /api/materials` - создание материала
- `PUT /api/materials/{id}/stock` - обновление запаса
- `PUT /api/materials/{id}` - обновление материала
- `DELETE /api/materials/{id}` - удаление материала

### ProductsApiController
- `GET /api/products` - список продуктов с фильтрацией по категории
- `GET /api/products/{id}/materials` - материалы для продукта
- `POST /api/products` - создание продукта с материалами
- `PUT /api/products/{id}` - обновление продукта
- `DELETE /api/products/{id}` - удаление продукта

### WorkOrdersApiController
- `GET /api/orders` - список заказов с фильтрацией
- `GET /api/orders/{id}/details` - детали заказа
- `POST /api/orders` - создание заказа с автоматическими расчетами
- `PUT /api/orders/{id}/progress` - обновление прогресса
- `PUT /api/orders/{id}/status` - изменение статуса

### ProductionLinesApiController
- `GET /api/lines` - список линий с фильтрацией
- `GET /api/lines/{id}/schedule` - расписание линии
- `PUT /api/lines/{id}/status` - изменение статуса
- `PUT /api/lines/{id}/efficiency` - изменение эффективности
- `POST /api/lines/{id}/assign` - назначение заказа

### CalculationApiController
- `POST /api/calculate/production` - расчет времени производства
- `POST /api/calculate/materials` - расчет потребности в материалах
- `POST /api/calculate/capacity` - расчет производственной мощности

## MVC Контроллеры и представления

### HomeController
- **Index** - главная панель управления с статистикой
- Отображение последних заказов, материалов с низким запасом
- Статистика производства в реальном времени

### MaterialsController
- **Index** - список материалов с поиском и фильтрацией
- **Create/Edit** - формы создания и редактирования
- **Details** - детальная информация о материале
- **Replenish** - быстрое пополнение запасов
- **Delete** - удаление с проверкой использования

### ProductsController
- **Index** - список продуктов с фильтрацией по категориям
- **Create/Edit** - формы с привязкой материалов
- **Details** - детальная информация с историей заказов
- **Delete** - удаление с проверкой активных заказов

### WorkOrdersController
- **Index** - список заказов с фильтрацией и статистикой
- **Create** - создание с автоматическими расчетами
- **Details** - полная информация по заказу
- **UpdateProgress/UpdateStatus** - управление заказом

### ProductionLinesController
- **Index** - интерактивная панель всех линий
- **Details** - детальная информация о линии
- **Schedule** - расписание производства
- **Create/Edit** - управление линиями
- **ToggleStatus** - переключение статуса

## Бизнес-логика и сервисы

### ProductionCalculationService
- **CalculateProductionTime** - расчет времени с учетом эффективности
- **CalculateEstimatedEndDate** - расчет даты завершения
- **CheckMaterialAvailability** - проверка наличия материалов
- **GetMaterialRequirements** - получение требований к материалам
- **FindAvailableProductionLine** - поиск доступной линии
- **ReserveMaterials/ReleaseMaterials** - управление резервированием

### Автоматические расчеты
- Время производства = (Количество × ВремяНаЕдиницу) / КоэффициентЭффективности
- Учет рабочих часов (8 часов в день, 5 дней в неделю)
- Автоматическая проверка материалов при создании заказа
- Уведомления о низком запасе

## Пользовательский интерфейс

### Responsive дизайн
- Адаптивная верстка для всех устройств
- Bootstrap 5 компоненты
- Современные иконки Bootstrap Icons
- Цветовая индикация статусов

### Интерактивные возможности
- AJAX обновления без перезагрузки
- Модальные окна для быстрых действий
- Автоматические расчеты в реальном времени
- Drag & drop интерфейсы
- Уведомления пользователю

### Ключевые особенности UI
- Цветовая маркировка статусов и приоритетов
- Прогресс-бары для отслеживания выполнения
- Быстрые фильтры и поиск
- Контекстные меню и действия
- Подтверждения критических операций

## Валидация и безопасность

### Валидация данных
- Аннотации данных в моделях
- Валидация на стороне клиента (JavaScript)
- Валидация на стороне сервера (ModelState)
- Проверка бизнес-правил в контроллерах

### Безопасность
- CSRF защита для всех форм
- Валидация входных данных
- Безопасные SQL запросы через EF Core
- Обработка ошибок и исключений

## Инициализация данных

### DbInitializer
- Автоматическое создание базы данных
- Заполнение тестовыми данными:
  - 7 материалов различных типов
  - 3 продукта с рецептурами
  - 4 производственные линии
  - 3 тестовых заказа
- Связи между продуктами и материалами

## Дополнительные возможности

### Статистика и аналитика
- Панель управления с ключевыми показателями
- Статистика производства по периодам
- Анализ загрузки производственных линий
- Отчеты по материалам и запасам

### Автоматизация
- Автоматический выбор производственных линий
- Расчет времени производства
- Уведомления о критических событиях
- Автоматическое обновление статусов

### Интеграционные возможности
- RESTful API для внешних систем
- JSON формат для обмена данными
- Расширяемая архитектура
- Готовность к интеграции с ERP системами

## Файлы и компоненты

### Основные файлы
- **Program.cs** - конфигурация приложения
- **ApplicationDbContext.cs** - контекст базы данных
- **site.css** - дополнительные стили
- **site.js** - JavaScript функции
- **run.bat** - скрипт запуска

### Представления
- **_Layout.cshtml** - основной макет с навигацией
- **Index.cshtml** - главная страница
- Специализированные представления для каждого контроллера
- Модальные окна и частичные представления

## Результат

Создана полнофункциональная система управления производством, которая:

✅ **Соответствует всем требованиям ТЗ**
✅ **Использует современные технологии**
✅ **Имеет интуитивный интерфейс**
✅ **Поддерживает все бизнес-процессы**
✅ **Готова к развертыванию и использованию**

Система может быть легко расширена и адаптирована под конкретные потребности предприятия.
