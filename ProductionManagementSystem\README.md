# Интерактивная система управления производством

Веб-приложение для управления производственными процессами, построенное на ASP.NET Core MVC с использованием Entity Framework Core.

## Возможности системы

### 🏭 Управление производством
- **Производственные линии**: Создание, управление и мониторинг производственных линий
- **Производственные заказы**: Планирование, отслеживание и управление заказами
- **Автоматические расчеты**: Время производства, потребность в материалах, планирование ресурсов

### 📦 Управление материалами
- **Склад материалов**: Учет и контроль запасов материалов
- **Уведомления о низком запасе**: Автоматические предупреждения
- **Быстрое пополнение**: Удобные инструменты для пополнения запасов

### 📊 Аналитика и отчетность
- **Интерактивная панель управления**: Статистика в реальном времени
- **Мониторинг производства**: Отслеживание прогресса заказов
- **Анализ эффективности**: Показатели производительности линий

### 🎯 Продукты и рецептуры
- **Каталог продуктов**: Управление номенклатурой продукции
- **Рецептуры**: Связь продуктов с необходимыми материалами
- **Категоризация**: Организация продуктов по категориям

## Технические характеристики

### Архитектура
- **Backend**: ASP.NET Core 9.0 MVC
- **База данных**: SQL Server / SQLite с Entity Framework Core
- **Frontend**: Bootstrap 5, JavaScript, jQuery
- **API**: RESTful API для интеграции

### Основные компоненты
- **Models**: Модели данных с валидацией
- **Controllers**: MVC и API контроллеры
- **Services**: Бизнес-логика и расчеты
- **Views**: Responsive веб-интерфейс

## Быстрый старт

### Требования
- .NET 9.0 SDK
- SQL Server (или SQLite для разработки)
- Современный веб-браузер

### Установка и запуск

1. **Клонирование проекта**
   ```bash
   git clone [repository-url]
   cd ProductionManagementSystem
   ```

2. **Восстановление пакетов**
   ```bash
   dotnet restore
   ```

3. **Сборка проекта**
   ```bash
   dotnet build
   ```

4. **Запуск приложения**
   ```bash
   dotnet run
   ```
   
   Или используйте готовый скрипт:
   ```bash
   run.bat
   ```

5. **Открытие в браузере**
   - HTTPS: https://localhost:7000
   - HTTP: http://localhost:5000

### Первый запуск
При первом запуске система автоматически:
- Создаст базу данных
- Заполнит тестовыми данными
- Настроит начальную конфигурацию

## Структура проекта

```
ProductionManagementSystem/
├── Controllers/           # MVC контроллеры
│   ├── Api/              # API контроллеры
│   ├── HomeController.cs
│   ├── MaterialsController.cs
│   ├── ProductsController.cs
│   ├── WorkOrdersController.cs
│   └── ProductionLinesController.cs
├── Models/               # Модели данных
│   ├── ViewModels/       # Модели представлений
│   ├── Product.cs
│   ├── Material.cs
│   ├── WorkOrder.cs
│   └── ProductionLine.cs
├── Data/                 # Контекст базы данных
│   ├── ApplicationDbContext.cs
│   └── DbInitializer.cs
├── Services/             # Бизнес-сервисы
│   └── ProductionCalculationService.cs
├── Views/                # Razor представления
│   ├── Home/
│   ├── Materials/
│   ├── Products/
│   ├── WorkOrders/
│   ├── ProductionLines/
│   └── Shared/
└── wwwroot/              # Статические файлы
    ├── css/
    ├── js/
    └── lib/
```

## API Endpoints

### Материалы
- `GET /api/materials` - Список материалов
- `GET /api/materials?lowStock=true` - Материалы с низким запасом
- `POST /api/materials` - Создание материала
- `PUT /api/materials/{id}/stock` - Обновление запаса

### Продукты
- `GET /api/products` - Список продуктов
- `GET /api/products/{id}/materials` - Материалы для продукта
- `POST /api/products` - Создание продукта

### Производственные заказы
- `GET /api/orders` - Список заказов
- `POST /api/orders` - Создание заказа
- `PUT /api/orders/{id}/progress` - Обновление прогресса
- `GET /api/orders/{id}/details` - Детали заказа

### Производственные линии
- `GET /api/lines` - Список линий
- `PUT /api/lines/{id}/status` - Изменение статуса
- `GET /api/lines/{id}/schedule` - Расписание линии

### Расчеты
- `POST /api/calculate/production` - Расчет времени производства
- `POST /api/calculate/materials` - Расчет потребности в материалах

## Функциональные возможности

### Панель управления
- Статистика в реальном времени
- Уведомления о критических событиях
- Быстрый доступ к основным функциям

### Управление материалами
- Добавление и редактирование материалов
- Контроль минимальных запасов
- Быстрое пополнение запасов
- Фильтрация и поиск

### Управление продуктами
- Создание продуктов с рецептурами
- Связь с материалами
- Категоризация продукции
- Расчет себестоимости

### Производственные заказы
- Создание заказов с автоматическими расчетами
- Отслеживание прогресса выполнения
- Управление статусами
- Назначение на производственные линии

### Производственные линии
- Мониторинг состояния линий
- Управление загрузкой
- Планирование производства
- Анализ эффективности

## Безопасность
- Валидация данных на клиенте и сервере
- CSRF защита
- Безопасные API endpoints
- Логирование операций

## Производительность
- Асинхронные операции
- Оптимизированные запросы к БД
- Кэширование статических ресурсов
- Responsive дизайн

## Поддержка и развитие
Система спроектирована для легкого расширения и модификации:
- Модульная архитектура
- Четкое разделение ответственности
- Документированный код
- Готовность к интеграции

## Лицензия
Этот проект создан в учебных целях.
