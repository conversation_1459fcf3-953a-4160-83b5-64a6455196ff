using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;
using ProductionManagementSystem.Services;

namespace ProductionManagementSystem.Controllers.Api
{
    [Route("api/orders")]
    [ApiController]
    public class WorkOrdersApiController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IProductionCalculationService _calculationService;

        public WorkOrdersApiController(ApplicationDbContext context, IProductionCalculationService calculationService)
        {
            _context = context;
            _calculationService = calculationService;
        }

        // GET: api/orders?status=Pending&date=2024-01-01
        [HttpGet]
        public async Task<ActionResult<IEnumerable<WorkOrder>>> GetWorkOrders(
            WorkOrderStatus? status = null, 
            DateTime? date = null)
        {
            var query = _context.WorkOrders
                .Include(wo => wo.Product)
                .Include(wo => wo.ProductionLine)
                .AsQueryable();

            if (status.HasValue)
            {
                query = query.Where(wo => wo.Status == status.Value);
            }

            if (date.HasValue)
            {
                query = query.Where(wo => wo.StartDate.Date == date.Value.Date);
            }

            var workOrders = await query
                .OrderByDescending(wo => wo.Id)
                .ToListAsync();

            return Ok(workOrders);
        }

        // GET: api/orders/5/details
        [HttpGet("{id}/details")]
        public async Task<ActionResult<object>> GetWorkOrderDetails(int id)
        {
            var workOrder = await _context.WorkOrders
                .Include(wo => wo.Product)
                .ThenInclude(p => p.ProductMaterials)
                .ThenInclude(pm => pm.Material)
                .Include(wo => wo.ProductionLine)
                .FirstOrDefaultAsync(wo => wo.Id == id);

            if (workOrder == null)
            {
                return NotFound();
            }

            var materialRequirements = await _calculationService
                .GetMaterialRequirementsAsync(workOrder.ProductId, workOrder.Quantity);

            var result = new
            {
                WorkOrder = workOrder,
                MaterialRequirements = materialRequirements,
                EstimatedDuration = workOrder.EstimatedDuration,
                IsOverdue = workOrder.IsOverdue
            };

            return Ok(result);
        }

        // POST: api/orders
        [HttpPost]
        public async Task<ActionResult<WorkOrder>> CreateWorkOrder(CreateWorkOrderRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Проверяем наличие материалов
            var hasSufficientMaterials = await _calculationService
                .CheckMaterialAvailabilityAsync(request.ProductId, request.Quantity);

            if (!hasSufficientMaterials)
            {
                return BadRequest("Недостаточно материалов для выполнения заказа");
            }

            // Находим доступную линию, если не указана
            ProductionLine? productionLine = null;
            if (request.ProductionLineId.HasValue)
            {
                productionLine = await _context.ProductionLines.FindAsync(request.ProductionLineId.Value);
                if (productionLine == null || !productionLine.IsAvailable)
                {
                    return BadRequest("Указанная производственная линия недоступна");
                }
            }
            else
            {
                productionLine = await _calculationService.FindAvailableProductionLineAsync();
            }

            // Рассчитываем время производства
            var efficiencyFactor = productionLine?.EfficiencyFactor ?? 1.0f;
            var productionTime = _calculationService.CalculateProductionTime(
                request.ProductId, request.Quantity, efficiencyFactor);
            var estimatedEndDate = _calculationService.CalculateEstimatedEndDate(
                request.StartDate, productionTime);

            var workOrder = new WorkOrder
            {
                ProductId = request.ProductId,
                ProductionLineId = productionLine?.Id,
                Quantity = request.Quantity,
                StartDate = request.StartDate,
                EstimatedEndDate = estimatedEndDate,
                Status = WorkOrderStatus.Pending
            };

            _context.WorkOrders.Add(workOrder);
            await _context.SaveChangesAsync();

            // Резервируем материалы
            await _calculationService.ReserveMaterialsAsync(request.ProductId, request.Quantity);

            // Обновляем статус производственной линии
            if (productionLine != null)
            {
                productionLine.CurrentWorkOrderId = workOrder.Id;
                await _context.SaveChangesAsync();
            }

            return CreatedAtAction(nameof(GetWorkOrderDetails), new { id = workOrder.Id }, workOrder);
        }

        // PUT: api/orders/5/progress
        [HttpPut("{id}/progress")]
        public async Task<IActionResult> UpdateProgress(int id, [FromBody] UpdateProgressRequest request)
        {
            var workOrder = await _context.WorkOrders.FindAsync(id);
            if (workOrder == null)
            {
                return NotFound();
            }

            if (request.Progress < 0 || request.Progress > 100)
            {
                return BadRequest("Прогресс должен быть от 0 до 100");
            }

            workOrder.Progress = request.Progress;

            // Автоматически обновляем статус
            if (request.Progress == 0 && workOrder.Status == WorkOrderStatus.Pending)
            {
                workOrder.Status = WorkOrderStatus.InProgress;
                workOrder.ActualStartDate = DateTime.Now;
            }
            else if (request.Progress == 100)
            {
                workOrder.Status = WorkOrderStatus.Completed;
                workOrder.ActualEndDate = DateTime.Now;

                // Освобождаем производственную линию
                if (workOrder.ProductionLineId.HasValue)
                {
                    var productionLine = await _context.ProductionLines.FindAsync(workOrder.ProductionLineId.Value);
                    if (productionLine != null)
                    {
                        productionLine.CurrentWorkOrderId = null;
                    }
                }
            }

            await _context.SaveChangesAsync();

            return Ok(new { workOrder.Id, workOrder.Progress, workOrder.Status });
        }

        // PUT: api/orders/5/status
        [HttpPut("{id}/status")]
        public async Task<IActionResult> UpdateStatus(int id, [FromBody] UpdateStatusRequest request)
        {
            var workOrder = await _context.WorkOrders
                .Include(wo => wo.ProductionLine)
                .FirstOrDefaultAsync(wo => wo.Id == id);

            if (workOrder == null)
            {
                return NotFound();
            }

            var oldStatus = workOrder.Status;
            workOrder.Status = request.Status;

            // Обработка изменения статуса
            switch (request.Status)
            {
                case WorkOrderStatus.InProgress:
                    if (oldStatus == WorkOrderStatus.Pending)
                    {
                        workOrder.ActualStartDate = DateTime.Now;
                    }
                    break;

                case WorkOrderStatus.Completed:
                    workOrder.ActualEndDate = DateTime.Now;
                    workOrder.Progress = 100;
                    // Освобождаем производственную линию
                    if (workOrder.ProductionLine != null)
                    {
                        workOrder.ProductionLine.CurrentWorkOrderId = null;
                    }
                    break;

                case WorkOrderStatus.Cancelled:
                    // Возвращаем материалы
                    await _calculationService.ReleaseMaterialsAsync(workOrder.ProductId, workOrder.Quantity);
                    // Освобождаем производственную линию
                    if (workOrder.ProductionLine != null)
                    {
                        workOrder.ProductionLine.CurrentWorkOrderId = null;
                    }
                    break;
            }

            await _context.SaveChangesAsync();

            return Ok(new { workOrder.Id, workOrder.Status });
        }
    }

    public class CreateWorkOrderRequest
    {
        public int ProductId { get; set; }
        public int? ProductionLineId { get; set; }
        public int Quantity { get; set; }
        public DateTime StartDate { get; set; }
    }

    public class UpdateProgressRequest
    {
        public int Progress { get; set; }
    }

    public class UpdateStatusRequest
    {
        public WorkOrderStatus Status { get; set; }
    }
}
