@model ProductionManagementSystem.Models.ViewModels.CreateWorkOrderViewModel
@{
    ViewData["Title"] = "Создать заказ";
}

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-plus-circle me-2"></i>Создать производственный заказ
                </h6>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post" id="createOrderForm">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="ProductId" class="form-label">Продукт</label>
                            <select asp-for="ProductId" class="form-select" id="productSelect" required>
                                <option value="">Выберите продукт</option>
                                @foreach (var product in Model.AvailableProducts)
                                {
                                    <option value="@product.Id" 
                                            data-production-time="@product.ProductionTimePerUnit"
                                            data-category="@product.Category">
                                        @product.Name (@product.Category)
                                    </option>
                                }
                            </select>
                            <span asp-validation-for="ProductId" class="text-danger"></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label asp-for="Quantity" class="form-label">Количество</label>
                            <input asp-for="Quantity" class="form-control" type="number" min="1" 
                                   id="quantityInput" placeholder="Введите количество" required />
                            <span asp-validation-for="Quantity" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="StartDate" class="form-label">Дата начала</label>
                            <input asp-for="StartDate" class="form-control" type="datetime-local" 
                                   id="startDateInput" required />
                            <span asp-validation-for="StartDate" class="text-danger"></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label asp-for="ProductionLineId" class="form-label">Производственная линия</label>
                            <select asp-for="ProductionLineId" class="form-select" id="productionLineSelect">
                                <option value="">Автоматический выбор</option>
                                @foreach (var line in Model.AvailableProductionLines)
                                {
                                    <option value="@line.Id" data-efficiency="@line.EfficiencyFactor">
                                        @line.Name (эффективность: @line.EfficiencyFactor)
                                    </option>
                                }
                            </select>
                            <div class="form-text">
                                Если не выбрано, система автоматически найдет доступную линию
                            </div>
                            <span asp-validation-for="ProductionLineId" class="text-danger"></span>
                        </div>
                    </div>

                    <!-- Контейнер для результатов расчета -->
                    <div id="calculation-results"></div>

                    <!-- Проверка материалов -->
                    <div id="material-check" style="display: none;">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Проверка материалов</h6>
                            </div>
                            <div class="card-body">
                                <div id="material-requirements"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-1"></i>Назад к списку
                                </a>
                                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                    <i class="bi bi-check-circle me-1"></i>Создать заказ
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        let calculationTimer;
        
        // Элементы формы
        const productSelect = document.getElementById('productSelect');
        const quantityInput = document.getElementById('quantityInput');
        const startDateInput = document.getElementById('startDateInput');
        const productionLineSelect = document.getElementById('productionLineSelect');
        const submitBtn = document.getElementById('submitBtn');
        const calculationResults = document.getElementById('calculation-results');
        const materialCheck = document.getElementById('material-check');
        const materialRequirements = document.getElementById('material-requirements');

        // Устанавливаем минимальную дату как текущее время
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        startDateInput.value = now.toISOString().slice(0, 16);

        // Обработчики событий
        productSelect.addEventListener('change', triggerCalculation);
        quantityInput.addEventListener('input', triggerCalculation);
        startDateInput.addEventListener('change', triggerCalculation);
        productionLineSelect.addEventListener('change', triggerCalculation);

        function triggerCalculation() {
            clearTimeout(calculationTimer);
            
            const productId = productSelect.value;
            const quantity = parseInt(quantityInput.value);
            const startDate = startDateInput.value;
            
            if (productId && quantity > 0 && startDate) {
                calculationTimer = setTimeout(() => {
                    calculateProduction(productId, quantity, startDate);
                }, 500);
            } else {
                hideCalculationResults();
            }
        }

        async function calculateProduction(productId, quantity, startDate) {
            try {
                const response = await fetch('/WorkOrders/Calculate?' + new URLSearchParams({
                    productId: productId,
                    quantity: quantity,
                    startDate: startDate
                }));

                if (response.ok) {
                    const data = await response.json();
                    displayCalculationResults(data);
                    await checkMaterials(productId, quantity);
                } else {
                    hideCalculationResults();
                }
            } catch (error) {
                console.error('Ошибка при расчете:', error);
                hideCalculationResults();
            }
        }

        function displayCalculationResults(data) {
            calculationResults.innerHTML = `
                <div class="card mt-3 ${data.hasSufficientMaterials ? 'border-success' : 'border-warning'}">
                    <div class="card-header ${data.hasSufficientMaterials ? 'bg-success' : 'bg-warning'} text-white">
                        <h6 class="mb-0">Результаты расчета</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Время производства:</strong><br>
                                <span class="h5 text-primary">${data.productionTimeHours} часов</span>
                                <small class="text-muted">(${data.productionTimeMinutes} минут)</small>
                            </div>
                            <div class="col-md-6">
                                <strong>Ожидаемая дата завершения:</strong><br>
                                <span class="h6">${formatDateTime(data.estimatedEndDate)}</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="alert ${data.hasSufficientMaterials ? 'alert-success' : 'alert-warning'} mb-0">
                                <i class="bi ${data.hasSufficientMaterials ? 'bi-check-circle' : 'bi-exclamation-triangle'} me-2"></i>
                                ${data.hasSufficientMaterials ? 'Все материалы в наличии' : 'Недостаточно материалов для производства'}
                            </div>
                        </div>
                        ${data.availableProductionLine ? `
                            <div class="mt-2">
                                <strong>Рекомендуемая линия:</strong> 
                                <span class="badge bg-info">${data.availableProductionLine.name}</span>
                                <small class="text-muted">(эффективность: ${data.availableProductionLine.efficiencyFactor})</small>
                            </div>
                        ` : '<div class="alert alert-info mt-2 mb-0">Нет доступных производственных линий</div>'}
                    </div>
                </div>
            `;

            // Включаем/отключаем кнопку отправки
            submitBtn.disabled = !data.hasSufficientMaterials || !data.availableProductionLine;
        }

        async function checkMaterials(productId, quantity) {
            try {
                const response = await fetch(`/api/products/${productId}/materials`);
                if (response.ok) {
                    const materials = await response.json();
                    displayMaterialRequirements(materials, quantity);
                }
            } catch (error) {
                console.error('Ошибка при проверке материалов:', error);
            }
        }

        function displayMaterialRequirements(materials, quantity) {
            if (materials.length === 0) {
                materialCheck.style.display = 'none';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>Материал</th><th>Требуется</th><th>Доступно</th><th>Статус</th></tr></thead><tbody>';

            materials.forEach(material => {
                const required = material.quantityNeeded * quantity;
                const available = material.availableQuantity;
                const sufficient = available >= required;

                html += `
                    <tr class="${sufficient ? '' : 'table-warning'}">
                        <td>${material.materialName}</td>
                        <td>${required.toFixed(2)} ${material.unitOfMeasure}</td>
                        <td>${available.toFixed(2)} ${material.unitOfMeasure}</td>
                        <td>
                            ${sufficient ? 
                                '<span class="badge bg-success">Достаточно</span>' : 
                                '<span class="badge bg-warning">Недостаточно</span>'
                            }
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            materialRequirements.innerHTML = html;
            materialCheck.style.display = 'block';
        }

        function hideCalculationResults() {
            calculationResults.innerHTML = '';
            materialCheck.style.display = 'none';
            submitBtn.disabled = true;
        }

        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('ru-RU', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Валидация формы перед отправкой
        document.getElementById('createOrderForm').addEventListener('submit', function(e) {
            if (submitBtn.disabled) {
                e.preventDefault();
                alert('Невозможно создать заказ. Проверьте наличие материалов и доступность производственных линий.');
            }
        });
    </script>
}
