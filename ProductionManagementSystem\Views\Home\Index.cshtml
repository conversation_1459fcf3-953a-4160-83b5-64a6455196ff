﻿@model ProductionManagementSystem.Models.ViewModels.DashboardViewModel
@{
    ViewData["Title"] = "Панель управления";
}

<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="bi bi-speedometer2 me-2"></i>Панель управления производством
        </h1>
    </div>
</div>

<!-- Статистические карточки -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Продукты</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalProducts</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-box-seam fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Материалы</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalMaterials</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-archive fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Активные линии</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.ActiveProductionLines</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-diagram-3 fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Заказы в работе</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.InProgressWorkOrders</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clipboard-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Предупреждения -->
@if (Model.OverdueWorkOrders > 0 || Model.LowStockMaterials > 0)
{
    <div class="row mb-4">
        @if (Model.OverdueWorkOrders > 0)
        {
            <div class="col-md-6 mb-3">
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading"><i class="bi bi-exclamation-triangle me-2"></i>Просроченные заказы!</h4>
                    <p>У вас есть <strong>@Model.OverdueWorkOrders</strong> просроченных заказов.</p>
                    <hr>
                    <p class="mb-0">
                        <a href="@Url.Action("Index", "WorkOrders", new { overdue = true })" class="btn btn-outline-danger">
                            Просмотреть заказы
                        </a>
                    </p>
                </div>
            </div>
        }
        @if (Model.LowStockMaterials > 0)
        {
            <div class="col-md-6 mb-3">
                <div class="alert alert-warning" role="alert">
                    <h4 class="alert-heading"><i class="bi bi-exclamation-circle me-2"></i>Низкий запас материалов!</h4>
                    <p>У вас есть <strong>@Model.LowStockMaterials</strong> материалов с низким запасом.</p>
                    <hr>
                    <p class="mb-0">
                        <a href="@Url.Action("Index", "Materials", new { lowStock = true })" class="btn btn-outline-warning">
                            Пополнить запасы
                        </a>
                    </p>
                </div>
            </div>
        }
    </div>
}

<!-- Основной контент -->
<div class="row">
    <!-- Последние заказы -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-check me-2"></i>Последние заказы
                </h6>
                <a href="@Url.Action("Index", "WorkOrders")" class="btn btn-sm btn-primary">
                    Все заказы <i class="bi bi-arrow-right"></i>
                </a>
            </div>
            <div class="card-body">
                @if (Model.RecentWorkOrders.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>№</th>
                                    <th>Продукт</th>
                                    <th>Количество</th>
                                    <th>Статус</th>
                                    <th>Дата начала</th>
                                    <th>Линия</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var order in Model.RecentWorkOrders)
                                {
                                    <tr>
                                        <td>
                                            <a href="@Url.Action("Details", "WorkOrders", new { id = order.Id })" class="text-decoration-none">
                                                #@order.Id
                                            </a>
                                        </td>
                                        <td>@order.Product.Name</td>
                                        <td>@order.Quantity</td>
                                        <td>
                                            @switch (order.Status)
                                            {
                                                case ProductionManagementSystem.Models.WorkOrderStatus.Pending:
                                                    <span class="badge bg-warning">Ожидание</span>
                                                    break;
                                                case ProductionManagementSystem.Models.WorkOrderStatus.InProgress:
                                                    <span class="badge bg-info">В работе</span>
                                                    break;
                                                case ProductionManagementSystem.Models.WorkOrderStatus.Completed:
                                                    <span class="badge bg-success">Завершен</span>
                                                    break;
                                                case ProductionManagementSystem.Models.WorkOrderStatus.Cancelled:
                                                    <span class="badge bg-danger">Отменен</span>
                                                    break;
                                            }
                                        </td>
                                        <td>@order.StartDate.ToString("dd.MM.yyyy")</td>
                                        <td>@(order.ProductionLine?.Name ?? "Не назначена")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard-x display-4 text-muted"></i>
                        <p class="text-muted mt-2">Нет заказов</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Боковая панель -->
    <div class="col-lg-4">
        <!-- Материалы с низким запасом -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>Низкий запас
                </h6>
            </div>
            <div class="card-body">
                @if (Model.LowStockMaterialsList.Any())
                {
                    @foreach (var material in Model.LowStockMaterialsList)
                    {
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-grow-1">
                                <div class="fw-bold">@material.Name</div>
                                <div class="text-muted small">
                                    @material.Quantity @material.UnitOfMeasure
                                    (мин: @material.MinimalStock)
                                </div>
                            </div>
                            <a href="@Url.Action("Replenish", "Materials", new { id = material.Id })"
                               class="btn btn-sm btn-outline-warning">
                                <i class="bi bi-plus-circle"></i>
                            </a>
                        </div>
                    }
                    <div class="text-center">
                        <a href="@Url.Action("Index", "Materials", new { lowStock = true })" class="btn btn-sm btn-warning">
                            Все материалы
                        </a>
                    </div>
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="bi bi-check-circle display-6 text-success"></i>
                        <p class="text-muted mt-2 mb-0">Все материалы в достатке</p>
                    </div>
                }
            </div>
        </div>

        <!-- Производственные линии -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="bi bi-diagram-3 me-2"></i>Производственные линии
                </h6>
            </div>
            <div class="card-body">
                @if (Model.ProductionLines.Any())
                {
                    @foreach (var line in Model.ProductionLines)
                    {
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div>
                                <div class="fw-bold">@line.Name</div>
                                <div class="text-muted small">
                                    @if (line.CurrentWorkOrder != null)
                                    {
                                        <span>Производит: @line.CurrentWorkOrder.Product.Name</span>
                                    }
                                    else
                                    {
                                        <span>Свободна</span>
                                    }
                                </div>
                            </div>
                            <div>
                                @if (line.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active)
                                {
                                    <span class="badge bg-success">Активна</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Остановлена</span>
                                }
                            </div>
                        </div>
                    }
                    <div class="text-center">
                        <a href="@Url.Action("Index", "ProductionLines")" class="btn btn-sm btn-info">
                            Управление линиями
                        </a>
                    </div>
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="bi bi-gear display-6 text-muted"></i>
                        <p class="text-muted mt-2 mb-0">Нет производственных линий</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Статистика производства -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="bi bi-graph-up me-2"></i>Статистика производства
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="border-end">
                            <div class="h4 text-success">@Model.TotalCompletedToday</div>
                            <div class="text-muted">Завершено сегодня</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border-end">
                            <div class="h4 text-info">@Model.TotalCompletedThisWeek</div>
                            <div class="text-muted">Завершено на этой неделе</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="h4 text-primary">@Model.TotalCompletedThisMonth</div>
                        <div class="text-muted">Завершено в этом месяце</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
