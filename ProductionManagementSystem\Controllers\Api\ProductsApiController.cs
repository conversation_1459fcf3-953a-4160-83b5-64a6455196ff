using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;

namespace ProductionManagementSystem.Controllers.Api
{
    [Route("api/products")]
    [ApiController]
    public class ProductsApiController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public ProductsApiController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/products?category=Electronics
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Product>>> GetProducts(string? category = null)
        {
            var query = _context.Products.AsQueryable();

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(p => p.Category.Contains(category));
            }

            var products = await query
                .Include(p => p.ProductMaterials)
                .ThenInclude(pm => pm.Material)
                .OrderBy(p => p.Name)
                .ToListAsync();

            return Ok(products);
        }

        // GET: api/products/5/materials
        [HttpGet("{id}/materials")]
        public async Task<ActionResult<IEnumerable<object>>> GetProductMaterials(int id)
        {
            var product = await _context.Products
                .Include(p => p.ProductMaterials)
                .ThenInclude(pm => pm.Material)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (product == null)
            {
                return NotFound();
            }

            var materials = product.ProductMaterials.Select(pm => new
            {
                MaterialId = pm.Material.Id,
                MaterialName = pm.Material.Name,
                QuantityNeeded = pm.QuantityNeeded,
                UnitOfMeasure = pm.Material.UnitOfMeasure,
                AvailableQuantity = pm.Material.Quantity,
                IsAvailable = pm.Material.Quantity >= pm.QuantityNeeded
            });

            return Ok(materials);
        }

        // GET: api/products/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Product>> GetProduct(int id)
        {
            var product = await _context.Products
                .Include(p => p.ProductMaterials)
                .ThenInclude(pm => pm.Material)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (product == null)
            {
                return NotFound();
            }

            return product;
        }

        // POST: api/products
        [HttpPost]
        public async Task<ActionResult<Product>> CreateProduct(CreateProductRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var product = new Product
            {
                Name = request.Name,
                Description = request.Description,
                Specifications = request.Specifications,
                Category = request.Category,
                MinimalStock = request.MinimalStock,
                ProductionTimePerUnit = request.ProductionTimePerUnit
            };

            _context.Products.Add(product);
            await _context.SaveChangesAsync();

            // Добавляем связи с материалами
            if (request.Materials != null && request.Materials.Any())
            {
                foreach (var materialReq in request.Materials)
                {
                    var productMaterial = new ProductMaterial
                    {
                        ProductId = product.Id,
                        MaterialId = materialReq.MaterialId,
                        QuantityNeeded = materialReq.QuantityNeeded
                    };
                    _context.ProductMaterials.Add(productMaterial);
                }
                await _context.SaveChangesAsync();
            }

            return CreatedAtAction(nameof(GetProduct), new { id = product.Id }, product);
        }

        // PUT: api/products/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProduct(int id, Product product)
        {
            if (id != product.Id)
            {
                return BadRequest();
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _context.Entry(product).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ProductExists(id))
                {
                    return NotFound();
                }
                throw;
            }

            return NoContent();
        }

        // DELETE: api/products/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProduct(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product == null)
            {
                return NotFound();
            }

            // Проверяем, есть ли активные заказы
            var hasActiveOrders = await _context.WorkOrders
                .AnyAsync(wo => wo.ProductId == id && 
                         (wo.Status == WorkOrderStatus.Pending || wo.Status == WorkOrderStatus.InProgress));

            if (hasActiveOrders)
            {
                return BadRequest("Нельзя удалить продукт с активными заказами");
            }

            _context.Products.Remove(product);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool ProductExists(int id)
        {
            return _context.Products.Any(e => e.Id == id);
        }
    }

    public class CreateProductRequest
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Specifications { get; set; }
        public string Category { get; set; } = string.Empty;
        public int MinimalStock { get; set; }
        public int ProductionTimePerUnit { get; set; }
        public List<ProductMaterialRequest>? Materials { get; set; }
    }

    public class ProductMaterialRequest
    {
        public int MaterialId { get; set; }
        public decimal QuantityNeeded { get; set; }
    }
}
