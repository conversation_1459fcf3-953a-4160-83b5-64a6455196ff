using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;

namespace ProductionManagementSystem.Controllers
{
    public class ProductionLinesController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ProductionLinesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: ProductionLines
        public async Task<IActionResult> Index()
        {
            var productionLines = await _context.ProductionLines
                .Include(pl => pl.CurrentWorkOrder)
                .ThenInclude(wo => wo!.Product)
                .OrderBy(pl => pl.Name)
                .ToListAsync();

            return View(productionLines);
        }

        // GET: ProductionLines/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var productionLine = await _context.ProductionLines
                .Include(pl => pl.CurrentWorkOrder)
                .ThenInclude(wo => wo!.Product)
                .Include(pl => pl.WorkOrders)
                .ThenInclude(wo => wo.Product)
                .FirstOrDefaultAsync(pl => pl.Id == id);

            if (productionLine == null)
            {
                return NotFound();
            }

            return View(productionLine);
        }

        // GET: ProductionLines/Schedule
        public async Task<IActionResult> Schedule()
        {
            var productionLines = await _context.ProductionLines
                .Include(pl => pl.WorkOrders.Where(wo => wo.Status != WorkOrderStatus.Cancelled))
                .ThenInclude(wo => wo.Product)
                .OrderBy(pl => pl.Name)
                .ToListAsync();

            return View(productionLines);
        }

        // GET: ProductionLines/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: ProductionLines/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Name,Status,EfficiencyFactor")] ProductionLine productionLine)
        {
            if (ModelState.IsValid)
            {
                _context.Add(productionLine);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Производственная линия успешно создана.";
                return RedirectToAction(nameof(Index));
            }
            return View(productionLine);
        }

        // GET: ProductionLines/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var productionLine = await _context.ProductionLines.FindAsync(id);
            if (productionLine == null)
            {
                return NotFound();
            }
            return View(productionLine);
        }

        // POST: ProductionLines/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,Status,EfficiencyFactor")] ProductionLine productionLine)
        {
            if (id != productionLine.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(productionLine);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Производственная линия успешно обновлена.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ProductionLineExists(productionLine.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(productionLine);
        }

        // POST: ProductionLines/ToggleStatus/5
        [HttpPost]
        public async Task<IActionResult> ToggleStatus(int id)
        {
            var productionLine = await _context.ProductionLines
                .Include(pl => pl.CurrentWorkOrder)
                .FirstOrDefaultAsync(pl => pl.Id == id);

            if (productionLine == null)
            {
                return NotFound();
            }

            // Проверяем, можно ли остановить линию
            if (productionLine.Status == ProductionLineStatus.Active && 
                productionLine.CurrentWorkOrder != null && 
                productionLine.CurrentWorkOrder.Status == WorkOrderStatus.InProgress)
            {
                TempData["ErrorMessage"] = "Нельзя остановить линию с активным заказом в процессе выполнения.";
                return RedirectToAction(nameof(Index));
            }

            // Переключаем статус
            productionLine.Status = productionLine.Status == ProductionLineStatus.Active 
                ? ProductionLineStatus.Stopped 
                : ProductionLineStatus.Active;

            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = $"Статус производственной линии '{productionLine.Name}' изменен на '{(productionLine.Status == ProductionLineStatus.Active ? "Активна" : "Остановлена")}'.";
            return RedirectToAction(nameof(Index));
        }

        // POST: ProductionLines/AssignOrder
        [HttpPost]
        public async Task<IActionResult> AssignOrder(int lineId, int orderId)
        {
            var productionLine = await _context.ProductionLines.FindAsync(lineId);
            if (productionLine == null)
            {
                return NotFound("Производственная линия не найдена");
            }

            if (!productionLine.IsAvailable)
            {
                TempData["ErrorMessage"] = "Производственная линия недоступна.";
                return RedirectToAction(nameof(Index));
            }

            var workOrder = await _context.WorkOrders.FindAsync(orderId);
            if (workOrder == null)
            {
                return NotFound("Заказ не найден");
            }

            if (workOrder.Status != WorkOrderStatus.Pending)
            {
                TempData["ErrorMessage"] = "Можно назначить только заказы в статусе 'Ожидание'.";
                return RedirectToAction(nameof(Index));
            }

            // Назначаем заказ на линию
            workOrder.ProductionLineId = lineId;
            productionLine.CurrentWorkOrderId = orderId;

            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = $"Заказ #{orderId} назначен на производственную линию '{productionLine.Name}'.";
            return RedirectToAction(nameof(Details), new { id = lineId });
        }

        // POST: ProductionLines/ReleaseOrder/5
        [HttpPost]
        public async Task<IActionResult> ReleaseOrder(int id)
        {
            var productionLine = await _context.ProductionLines
                .Include(pl => pl.CurrentWorkOrder)
                .FirstOrDefaultAsync(pl => pl.Id == id);

            if (productionLine == null)
            {
                return NotFound();
            }

            if (productionLine.CurrentWorkOrder == null)
            {
                TempData["ErrorMessage"] = "На линии нет активного заказа.";
                return RedirectToAction(nameof(Index));
            }

            // Проверяем, можно ли освободить линию
            if (productionLine.CurrentWorkOrder.Status == WorkOrderStatus.InProgress)
            {
                TempData["ErrorMessage"] = "Нельзя освободить линию с заказом в процессе выполнения.";
                return RedirectToAction(nameof(Index));
            }

            // Освобождаем линию
            var workOrder = productionLine.CurrentWorkOrder;
            workOrder.ProductionLineId = null;
            productionLine.CurrentWorkOrderId = null;

            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = $"Заказ #{workOrder.Id} снят с производственной линии '{productionLine.Name}'.";
            return RedirectToAction(nameof(Details), new { id });
        }

        // GET: ProductionLines/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var productionLine = await _context.ProductionLines
                .Include(pl => pl.CurrentWorkOrder)
                .Include(pl => pl.WorkOrders)
                .FirstOrDefaultAsync(pl => pl.Id == id);

            if (productionLine == null)
            {
                return NotFound();
            }

            // Проверяем, есть ли активные заказы
            var hasActiveOrders = productionLine.WorkOrders
                .Any(wo => wo.Status == WorkOrderStatus.Pending || wo.Status == WorkOrderStatus.InProgress);

            ViewBag.HasActiveOrders = hasActiveOrders;

            return View(productionLine);
        }

        // POST: ProductionLines/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var productionLine = await _context.ProductionLines
                .Include(pl => pl.WorkOrders)
                .FirstOrDefaultAsync(pl => pl.Id == id);

            if (productionLine != null)
            {
                // Проверяем, есть ли активные заказы
                var hasActiveOrders = productionLine.WorkOrders
                    .Any(wo => wo.Status == WorkOrderStatus.Pending || wo.Status == WorkOrderStatus.InProgress);

                if (hasActiveOrders)
                {
                    TempData["ErrorMessage"] = "Нельзя удалить производственную линию с активными заказами.";
                    return RedirectToAction(nameof(Index));
                }

                _context.ProductionLines.Remove(productionLine);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Производственная линия успешно удалена.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: ProductionLines/GetAvailableOrders
        public async Task<IActionResult> GetAvailableOrders()
        {
            var availableOrders = await _context.WorkOrders
                .Include(wo => wo.Product)
                .Where(wo => wo.Status == WorkOrderStatus.Pending && wo.ProductionLineId == null)
                .OrderBy(wo => wo.StartDate)
                .Select(wo => new
                {
                    wo.Id,
                    ProductName = wo.Product.Name,
                    wo.Quantity,
                    StartDate = wo.StartDate.ToString("dd.MM.yyyy HH:mm"),
                    EstimatedEndDate = wo.EstimatedEndDate.ToString("dd.MM.yyyy HH:mm")
                })
                .ToListAsync();

            return Json(availableOrders);
        }

        private bool ProductionLineExists(int id)
        {
            return _context.ProductionLines.Any(e => e.Id == id);
        }
    }
}
