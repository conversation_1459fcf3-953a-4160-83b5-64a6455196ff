@model ProductionManagementSystem.Models.Material
@{
    ViewData["Title"] = "Добавить материал";
}

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-plus-circle me-2"></i>Добавить новый материал
                </h6>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Name" class="form-label">Название материала</label>
                            <input asp-for="Name" class="form-control" placeholder="Введите название материала" />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label asp-for="UnitOfMeasure" class="form-label">Единица измерения</label>
                            <input asp-for="UnitOfMeasure" class="form-control" placeholder="кг, л, шт, м и т.д." />
                            <span asp-validation-for="UnitOfMeasure" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Quantity" class="form-label">Текущее количество</label>
                            <div class="input-group">
                                <input asp-for="Quantity" class="form-control" type="number" step="0.01" min="0" 
                                       placeholder="0.00" />
                                <span class="input-group-text" id="current-unit">-</span>
                            </div>
                            <span asp-validation-for="Quantity" class="text-danger"></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label asp-for="MinimalStock" class="form-label">Минимальный запас</label>
                            <div class="input-group">
                                <input asp-for="MinimalStock" class="form-control" type="number" step="0.01" min="0" 
                                       placeholder="0.00" />
                                <span class="input-group-text" id="minimal-unit">-</span>
                            </div>
                            <span asp-validation-for="MinimalStock" class="text-danger"></span>
                            <div class="form-text">
                                При достижении этого уровня будет отображаться предупреждение о низком запасе
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-1"></i>Назад к списку
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-1"></i>Создать материал
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Обновление единиц измерения в полях ввода
        document.getElementById('UnitOfMeasure').addEventListener('input', function() {
            const unit = this.value || '-';
            document.getElementById('current-unit').textContent = unit;
            document.getElementById('minimal-unit').textContent = unit;
        });
        
        // Валидация на стороне клиента
        document.querySelector('form').addEventListener('submit', function(e) {
            const quantity = parseFloat(document.getElementById('Quantity').value);
            const minimalStock = parseFloat(document.getElementById('MinimalStock').value);
            
            if (quantity < 0) {
                e.preventDefault();
                alert('Текущее количество не может быть отрицательным');
                return;
            }
            
            if (minimalStock < 0) {
                e.preventDefault();
                alert('Минимальный запас не может быть отрицательным');
                return;
            }
            
            if (quantity < minimalStock) {
                const confirmed = confirm('Текущее количество меньше минимального запаса. Продолжить?');
                if (!confirmed) {
                    e.preventDefault();
                }
            }
        });
    </script>
}
