using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ProductionManagementSystem.Models
{
    public class ProductMaterial
    {
        public int ProductId { get; set; }
        public int MaterialId { get; set; }

        [Range(0.01, double.MaxValue)]
        [Column(TypeName = "decimal(18,2)")]
        public decimal QuantityNeeded { get; set; }

        // Navigation properties
        public virtual Product Product { get; set; } = null!;
        public virtual Material Material { get; set; } = null!;
    }
}
