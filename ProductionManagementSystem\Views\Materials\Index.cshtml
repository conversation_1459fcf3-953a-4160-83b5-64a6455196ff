@model IEnumerable<ProductionManagementSystem.Models.Material>
@{
    ViewData["Title"] = "Материалы";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="bi bi-archive me-2"></i>Управление материалами
    </h1>
    <a asp-action="Create" class="btn btn-primary">
        <i class="bi bi-plus-circle me-1"></i>Добавить материал
    </a>
</div>

<!-- Фильтры и поиск -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Поиск</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="@ViewBag.Search" placeholder="Название материала или единица измерения">
            </div>
            <div class="col-md-3">
                <label for="lowStock" class="form-label">Фильтр</label>
                <select class="form-select" id="lowStock" name="lowStock">
                    <option value="">Все материалы</option>
                    <option value="true" selected="@(ViewBag.LowStock == true)">Только с низким запасом</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-search"></i> Поиск
                </button>
                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i> Сброс
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Таблица материалов -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            Список материалов (@Model.Count())
        </h6>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover" id="materialsTable">
                    <thead class="table-light">
                        <tr>
                            <th>Название</th>
                            <th>Количество</th>
                            <th>Единица измерения</th>
                            <th>Минимальный запас</th>
                            <th>Статус</th>
                            <th>Действия</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var material in Model)
                        {
                            <tr class="@(material.IsLowStock ? "table-warning" : "")">
                                <td>
                                    <div class="fw-bold">@material.Name</div>
                                </td>
                                <td>
                                    <span class="material-quantity" data-id="@material.Id">
                                        @material.Quantity.ToString("N2")
                                    </span>
                                </td>
                                <td>@material.UnitOfMeasure</td>
                                <td>@material.MinimalStock.ToString("N2")</td>
                                <td>
                                    @if (material.IsLowStock)
                                    {
                                        <span class="badge bg-warning text-dark">
                                            <i class="bi bi-exclamation-triangle me-1"></i>Низкий запас
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>В наличии
                                        </span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@material.Id"
                                           class="btn btn-sm btn-outline-info" title="Подробности">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@material.Id"
                                           class="btn btn-sm btn-outline-primary" title="Редактировать">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a asp-action="Replenish" asp-route-id="@material.Id"
                                           class="btn btn-sm btn-outline-success" title="Пополнить запас">
                                            <i class="bi bi-plus-circle"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@material.Id"
                                           class="btn btn-sm btn-outline-danger" title="Удалить">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-archive display-1 text-muted"></i>
                <h4 class="text-muted mt-3">Материалы не найдены</h4>
                <p class="text-muted">
                    @if (!string.IsNullOrEmpty(ViewBag.Search as string) || ViewBag.LowStock == true)
                    {
                        <span>Попробуйте изменить параметры поиска или </span>
                        <a href="@Url.Action("Index")" class="text-decoration-none">сбросить фильтры</a>
                    }
                    else
                    {
                        <span>Начните с </span>
                        <a asp-action="Create" class="text-decoration-none">добавления первого материала</a>
                    }
                </p>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        // Автоматическое обновление количества материалов
        function updateMaterialQuantity(materialId, newQuantity) {
            fetch(`/api/materials/${materialId}/stock`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ quantity: newQuantity })
            })
            .then(response => response.json())
            .then(data => {
                if (data.isLowStock) {
                    // Обновляем строку, если запас стал низким
                    location.reload();
                } else {
                    // Обновляем только количество
                    document.querySelector(`[data-id="${materialId}"]`).textContent = newQuantity.toFixed(2);
                }
            })
            .catch(error => {
                console.error('Ошибка при обновлении количества:', error);
                alert('Ошибка при обновлении количества материала');
            });
        }

        // Инициализация DataTable для сортировки и поиска
        $(document).ready(function() {
            if ($('#materialsTable tbody tr').length > 0) {
                $('#materialsTable').DataTable({
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Russian.json"
                    },
                    "pageLength": 25,
                    "order": [[0, "asc"]],
                    "columnDefs": [
                        { "orderable": false, "targets": 5 } // Отключаем сортировку для колонки действий
                    ]
                });
            }
        });
    </script>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css">
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
}
