@model ProductionManagementSystem.Models.WorkOrder
@{
    ViewData["Title"] = $"Заказ #{Model.Id}";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="bi bi-clipboard-check me-2"></i>Заказ #@Model.Id
    </h1>
    <div>
        <a asp-action="Index" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i>Назад к списку
        </a>
        @if (Model.Status == ProductionManagementSystem.Models.WorkOrderStatus.Pending || 
             Model.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
        {
            <button type="button" class="btn btn-outline-danger" onclick="cancelOrder(@Model.Id)">
                <i class="bi bi-x-circle me-1"></i>Отменить заказ
            </button>
        }
    </div>
</div>

<!-- Основная информация -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Информация о заказе</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Продукт:</td>
                                <td>@Model.Product.Name</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Категория:</td>
                                <td>@Model.Product.Category</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Количество:</td>
                                <td>@Model.Quantity</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Статус:</td>
                                <td>
                                    @switch (Model.Status)
                                    {
                                        case ProductionManagementSystem.Models.WorkOrderStatus.Pending:
                                            <span class="badge bg-warning text-dark">
                                                <i class="bi bi-clock me-1"></i>Ожидание
                                            </span>
                                            break;
                                        case ProductionManagementSystem.Models.WorkOrderStatus.InProgress:
                                            <span class="badge bg-info">
                                                <i class="bi bi-play-circle me-1"></i>В работе
                                            </span>
                                            break;
                                        case ProductionManagementSystem.Models.WorkOrderStatus.Completed:
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>Завершен
                                            </span>
                                            break;
                                        case ProductionManagementSystem.Models.WorkOrderStatus.Cancelled:
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle me-1"></i>Отменен
                                            </span>
                                            break;
                                    }
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Дата начала:</td>
                                <td>@Model.StartDate.ToString("dd.MM.yyyy HH:mm")</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Плановое завершение:</td>
                                <td>
                                    @Model.EstimatedEndDate.ToString("dd.MM.yyyy HH:mm")
                                    @if (Model.IsOverdue)
                                    {
                                        <br><small class="text-danger">
                                            <i class="bi bi-exclamation-triangle"></i> 
                                            Просрочен на @((DateTime.Now - Model.EstimatedEndDate).Days) дн.
                                        </small>
                                    }
                                </td>
                            </tr>
                            @if (Model.ActualStartDate.HasValue)
                            {
                                <tr>
                                    <td class="fw-bold">Фактическое начало:</td>
                                    <td>@Model.ActualStartDate.Value.ToString("dd.MM.yyyy HH:mm")</td>
                                </tr>
                            }
                            @if (Model.ActualEndDate.HasValue)
                            {
                                <tr>
                                    <td class="fw-bold">Фактическое завершение:</td>
                                    <td>@Model.ActualEndDate.Value.ToString("dd.MM.yyyy HH:mm")</td>
                                </tr>
                            }
                            <tr>
                                <td class="fw-bold">Производственная линия:</td>
                                <td>
                                    @if (Model.ProductionLine != null)
                                    {
                                        <a asp-controller="ProductionLines" asp-action="Details" asp-route-id="@Model.ProductionLine.Id" 
                                           class="text-decoration-none">
                                            @Model.ProductionLine.Name
                                        </a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Не назначена</span>
                                    }
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Прогресс выполнения -->
                @if (Model.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
                {
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Прогресс выполнения</h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                    onclick="showProgressModal(@Model.Id, @Model.Progress)">
                                <i class="bi bi-arrow-up-circle me-1"></i>Обновить
                            </button>
                        </div>
                        <div class="progress" style="height: 25px;">
                            <div class="progress-bar @(Model.Progress < 50 ? "bg-warning" : Model.Progress < 100 ? "bg-info" : "bg-success") progress-bar-striped progress-bar-animated" 
                                 role="progressbar" 
                                 style="width: @Model.Progress%" 
                                 aria-valuenow="@Model.Progress" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                                @Model.Progress%
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Боковая панель -->
    <div class="col-lg-4">
        <!-- Временные показатели -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">Временные показатели</h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-3">
                        <div class="h4 text-primary">@Model.EstimatedDuration.TotalHours.ToString("F1")</div>
                        <div class="text-muted">Плановое время (часы)</div>
                    </div>
                    @if (Model.ActualStartDate.HasValue && Model.ActualEndDate.HasValue)
                    {
                        <div class="mb-3">
                            <div class="h4 text-success">@((Model.ActualEndDate.Value - Model.ActualStartDate.Value).TotalHours.ToString("F1"))</div>
                            <div class="text-muted">Фактическое время (часы)</div>
                        </div>
                    }
                    @if (Model.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress && Model.ActualStartDate.HasValue)
                    {
                        <div class="mb-3">
                            <div class="h4 text-info">@((DateTime.Now - Model.ActualStartDate.Value).TotalHours.ToString("F1"))</div>
                            <div class="text-muted">Время в работе (часы)</div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Быстрые действия -->
        @if (Model.Status == ProductionManagementSystem.Models.WorkOrderStatus.Pending)
        {
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Быстрые действия</h6>
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-success w-100 mb-2" onclick="startOrder(@Model.Id)">
                        <i class="bi bi-play-circle me-1"></i>Начать производство
                    </button>
                    <button type="button" class="btn btn-outline-info w-100 mb-2" onclick="assignToLine(@Model.Id)">
                        <i class="bi bi-diagram-3 me-1"></i>Назначить на линию
                    </button>
                </div>
            </div>
        }
        @if (Model.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
        {
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Быстрые действия</h6>
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-success w-100 mb-2" onclick="completeOrder(@Model.Id)">
                        <i class="bi bi-check-circle me-1"></i>Завершить заказ
                    </button>
                    <button type="button" class="btn btn-outline-warning w-100 mb-2" onclick="pauseOrder(@Model.Id)">
                        <i class="bi bi-pause-circle me-1"></i>Приостановить
                    </button>
                </div>
            </div>
        }
    </div>
</div>

<!-- Требования к материалам -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">Требования к материалам</h6>
            </div>
            <div class="card-body">
                @if (Model.Product.ProductMaterials.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Материал</th>
                                    <th>Требуется на единицу</th>
                                    <th>Общее требование</th>
                                    <th>Доступно</th>
                                    <th>Единица измерения</th>
                                    <th>Статус</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var pm in Model.Product.ProductMaterials)
                                {
                                    var totalRequired = pm.QuantityNeeded * Model.Quantity;
                                    var sufficient = pm.Material.Quantity >= totalRequired;
                                    
                                    <tr class="@(sufficient ? "" : "table-warning")">
                                        <td>
                                            <a asp-controller="Materials" asp-action="Details" asp-route-id="@pm.Material.Id" 
                                               class="text-decoration-none">
                                                @pm.Material.Name
                                            </a>
                                        </td>
                                        <td>@pm.QuantityNeeded.ToString("N2")</td>
                                        <td class="fw-bold">@totalRequired.ToString("N2")</td>
                                        <td>@pm.Material.Quantity.ToString("N2")</td>
                                        <td>@pm.Material.UnitOfMeasure</td>
                                        <td>
                                            @if (sufficient)
                                            {
                                                <span class="badge bg-success">
                                                    <i class="bi bi-check-circle me-1"></i>Достаточно
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning text-dark">
                                                    <i class="bi bi-exclamation-triangle me-1"></i>Недостаточно
                                                </span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="bi bi-archive display-6 text-muted"></i>
                        <p class="text-muted mt-2 mb-0">Для этого продукта не требуются материалы</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно для обновления прогресса -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="progressModalLabel">Обновить прогресс заказа #@Model.Id</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="progressForm">
                    <input type="hidden" id="progressOrderId" value="@Model.Id" />
                    <div class="mb-3">
                        <label for="progressValue" class="form-label">Прогресс выполнения (%)</label>
                        <input type="range" class="form-range" id="progressValue" min="0" max="100" step="5" value="@Model.Progress" />
                        <div class="d-flex justify-content-between">
                            <span>0%</span>
                            <span id="progressDisplay">@Model.Progress%</span>
                            <span>100%</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                <button type="button" class="btn btn-primary" onclick="updateProgress()">Обновить</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Показать модальное окно прогресса
        function showProgressModal(orderId, currentProgress) {
            document.getElementById('progressOrderId').value = orderId;
            document.getElementById('progressValue').value = currentProgress;
            document.getElementById('progressDisplay').textContent = currentProgress + '%';
            
            new bootstrap.Modal(document.getElementById('progressModal')).show();
        }

        // Обновление отображения прогресса
        document.getElementById('progressValue').addEventListener('input', function() {
            document.getElementById('progressDisplay').textContent = this.value + '%';
        });

        // Обновить прогресс заказа
        async function updateProgress() {
            const orderId = document.getElementById('progressOrderId').value;
            const progress = document.getElementById('progressValue').value;
            
            try {
                const response = await fetch(`/WorkOrders/UpdateProgress/${orderId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `progress=${progress}&__RequestVerificationToken=${getAntiForgeryToken()}`
                });

                if (response.ok) {
                    bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
                    location.reload();
                } else {
                    alert('Ошибка при обновлении прогресса');
                }
            } catch (error) {
                console.error('Ошибка:', error);
                alert('Ошибка при обновлении прогресса');
            }
        }

        // Быстрые действия
        async function startOrder(orderId) {
            await updateOrderStatus(orderId, 'InProgress');
        }

        async function completeOrder(orderId) {
            if (confirm('Вы уверены, что хотите завершить этот заказ?')) {
                await updateOrderStatus(orderId, 'Completed');
            }
        }

        async function pauseOrder(orderId) {
            await updateOrderStatus(orderId, 'Pending');
        }

        async function cancelOrder(orderId) {
            if (confirm('Вы уверены, что хотите отменить этот заказ?')) {
                await updateOrderStatus(orderId, 'Cancelled');
            }
        }

        // Обновление статуса заказа
        async function updateOrderStatus(orderId, status) {
            try {
                const response = await fetch(`/WorkOrders/UpdateStatus/${orderId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `status=${status}&__RequestVerificationToken=${getAntiForgeryToken()}`
                });

                if (response.ok) {
                    location.reload();
                } else {
                    alert('Ошибка при обновлении статуса заказа');
                }
            } catch (error) {
                console.error('Ошибка:', error);
                alert('Ошибка при обновлении статуса заказа');
            }
        }

        // Получение CSRF токена
        function getAntiForgeryToken() {
            const token = document.querySelector('input[name="__RequestVerificationToken"]');
            return token ? token.value : '';
        }
    </script>
}
