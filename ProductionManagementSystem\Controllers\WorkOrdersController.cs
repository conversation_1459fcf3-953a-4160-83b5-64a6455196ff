using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;
using ProductionManagementSystem.Models.ViewModels;
using ProductionManagementSystem.Services;

namespace ProductionManagementSystem.Controllers
{
    public class WorkOrdersController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IProductionCalculationService _calculationService;

        public WorkOrdersController(ApplicationDbContext context, IProductionCalculationService calculationService)
        {
            _context = context;
            _calculationService = calculationService;
        }

        // GET: WorkOrders
        public async Task<IActionResult> Index(WorkOrderStatus? status, bool? overdue, DateTime? date)
        {
            var query = _context.WorkOrders
                .Include(wo => wo.Product)
                .Include(wo => wo.ProductionLine)
                .AsQueryable();

            if (status.HasValue)
            {
                query = query.Where(wo => wo.Status == status.Value);
            }

            if (overdue == true)
            {
                query = query.Where(wo => wo.Status != WorkOrderStatus.Completed && 
                                         wo.Status != WorkOrderStatus.Cancelled && 
                                         wo.EstimatedEndDate < DateTime.Now);
            }

            if (date.HasValue)
            {
                query = query.Where(wo => wo.StartDate.Date == date.Value.Date);
            }

            var workOrders = await query
                .OrderByDescending(wo => wo.Id)
                .ToListAsync();

            ViewBag.Status = status;
            ViewBag.Overdue = overdue;
            ViewBag.Date = date;

            return View(workOrders);
        }

        // GET: WorkOrders/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var workOrder = await _context.WorkOrders
                .Include(wo => wo.Product)
                .ThenInclude(p => p.ProductMaterials)
                .ThenInclude(pm => pm.Material)
                .Include(wo => wo.ProductionLine)
                .FirstOrDefaultAsync(wo => wo.Id == id);

            if (workOrder == null)
            {
                return NotFound();
            }

            return View(workOrder);
        }

        // GET: WorkOrders/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = new CreateWorkOrderViewModel
            {
                StartDate = DateTime.Now,
                AvailableProducts = await _context.Products.OrderBy(p => p.Name).ToListAsync(),
                AvailableProductionLines = await _context.ProductionLines
                    .Where(pl => pl.Status == ProductionLineStatus.Active && pl.CurrentWorkOrderId == null)
                    .OrderBy(pl => pl.Name)
                    .ToListAsync()
            };

            return View(viewModel);
        }

        // POST: WorkOrders/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateWorkOrderViewModel model)
        {
            if (ModelState.IsValid)
            {
                // Проверяем наличие материалов
                var hasSufficientMaterials = await _calculationService
                    .CheckMaterialAvailabilityAsync(model.ProductId, model.Quantity);

                if (!hasSufficientMaterials)
                {
                    ModelState.AddModelError("", "Недостаточно материалов для выполнения заказа");
                    model.MaterialRequirements = await _calculationService
                        .GetMaterialRequirementsAsync(model.ProductId, model.Quantity);
                }
                else
                {
                    // Находим доступную линию, если не указана
                    ProductionLine? productionLine = null;
                    if (model.ProductionLineId.HasValue)
                    {
                        productionLine = await _context.ProductionLines.FindAsync(model.ProductionLineId.Value);
                        if (productionLine == null || !productionLine.IsAvailable)
                        {
                            ModelState.AddModelError("ProductionLineId", "Указанная производственная линия недоступна");
                        }
                    }
                    else
                    {
                        productionLine = await _calculationService.FindAvailableProductionLineAsync();
                    }

                    if (ModelState.IsValid)
                    {
                        // Рассчитываем время производства
                        var efficiencyFactor = productionLine?.EfficiencyFactor ?? 1.0f;
                        var productionTime = _calculationService.CalculateProductionTime(
                            model.ProductId, model.Quantity, efficiencyFactor);
                        var estimatedEndDate = _calculationService.CalculateEstimatedEndDate(
                            model.StartDate, productionTime);

                        var workOrder = new WorkOrder
                        {
                            ProductId = model.ProductId,
                            ProductionLineId = productionLine?.Id,
                            Quantity = model.Quantity,
                            StartDate = model.StartDate,
                            EstimatedEndDate = estimatedEndDate,
                            Status = WorkOrderStatus.Pending
                        };

                        _context.WorkOrders.Add(workOrder);
                        await _context.SaveChangesAsync();

                        // Резервируем материалы
                        await _calculationService.ReserveMaterialsAsync(model.ProductId, model.Quantity);

                        // Обновляем статус производственной линии
                        if (productionLine != null)
                        {
                            productionLine.CurrentWorkOrderId = workOrder.Id;
                            await _context.SaveChangesAsync();
                        }

                        TempData["SuccessMessage"] = "Производственный заказ успешно создан.";
                        return RedirectToAction(nameof(Details), new { id = workOrder.Id });
                    }
                }
            }

            // Если модель невалидна, перезагружаем данные
            model.AvailableProducts = await _context.Products.OrderBy(p => p.Name).ToListAsync();
            model.AvailableProductionLines = await _context.ProductionLines
                .Where(pl => pl.Status == ProductionLineStatus.Active && pl.CurrentWorkOrderId == null)
                .OrderBy(pl => pl.Name)
                .ToListAsync();

            return View(model);
        }

        // POST: WorkOrders/UpdateProgress/5
        [HttpPost]
        public async Task<IActionResult> UpdateProgress(int id, int progress)
        {
            var workOrder = await _context.WorkOrders.FindAsync(id);
            if (workOrder == null)
            {
                return NotFound();
            }

            if (progress < 0 || progress > 100)
            {
                return BadRequest("Прогресс должен быть от 0 до 100");
            }

            workOrder.Progress = progress;

            // Автоматически обновляем статус
            if (progress == 0 && workOrder.Status == WorkOrderStatus.Pending)
            {
                workOrder.Status = WorkOrderStatus.InProgress;
                workOrder.ActualStartDate = DateTime.Now;
            }
            else if (progress == 100)
            {
                workOrder.Status = WorkOrderStatus.Completed;
                workOrder.ActualEndDate = DateTime.Now;

                // Освобождаем производственную линию
                if (workOrder.ProductionLineId.HasValue)
                {
                    var productionLine = await _context.ProductionLines.FindAsync(workOrder.ProductionLineId.Value);
                    if (productionLine != null)
                    {
                        productionLine.CurrentWorkOrderId = null;
                    }
                }
            }

            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Прогресс заказа обновлен.";
            return RedirectToAction(nameof(Details), new { id });
        }

        // POST: WorkOrders/UpdateStatus/5
        [HttpPost]
        public async Task<IActionResult> UpdateStatus(int id, WorkOrderStatus status)
        {
            var workOrder = await _context.WorkOrders
                .Include(wo => wo.ProductionLine)
                .FirstOrDefaultAsync(wo => wo.Id == id);

            if (workOrder == null)
            {
                return NotFound();
            }

            var oldStatus = workOrder.Status;
            workOrder.Status = status;

            // Обработка изменения статуса
            switch (status)
            {
                case WorkOrderStatus.InProgress:
                    if (oldStatus == WorkOrderStatus.Pending)
                    {
                        workOrder.ActualStartDate = DateTime.Now;
                    }
                    break;

                case WorkOrderStatus.Completed:
                    workOrder.ActualEndDate = DateTime.Now;
                    workOrder.Progress = 100;
                    // Освобождаем производственную линию
                    if (workOrder.ProductionLine != null)
                    {
                        workOrder.ProductionLine.CurrentWorkOrderId = null;
                    }
                    break;

                case WorkOrderStatus.Cancelled:
                    // Возвращаем материалы
                    await _calculationService.ReleaseMaterialsAsync(workOrder.ProductId, workOrder.Quantity);
                    // Освобождаем производственную линию
                    if (workOrder.ProductionLine != null)
                    {
                        workOrder.ProductionLine.CurrentWorkOrderId = null;
                    }
                    break;
            }

            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Статус заказа обновлен.";
            return RedirectToAction(nameof(Details), new { id });
        }

        // GET: WorkOrders/Calculate
        [HttpGet]
        public async Task<IActionResult> CalculateProduction(int productId, int quantity, DateTime startDate)
        {
            if (productId == 0 || quantity <= 0)
            {
                return BadRequest();
            }

            var productionLine = await _calculationService.FindAvailableProductionLineAsync();
            var efficiencyFactor = productionLine?.EfficiencyFactor ?? 1.0f;

            var productionTime = _calculationService.CalculateProductionTime(productId, quantity, efficiencyFactor);
            var estimatedEndDate = _calculationService.CalculateEstimatedEndDate(startDate, productionTime);
            var hasSufficientMaterials = await _calculationService.CheckMaterialAvailabilityAsync(productId, quantity);
            var materialRequirements = await _calculationService.GetMaterialRequirementsAsync(productId, quantity);

            return Json(new
            {
                productionTimeMinutes = productionTime,
                productionTimeHours = Math.Round(productionTime / 60.0, 2),
                estimatedEndDate = estimatedEndDate.ToString("yyyy-MM-dd HH:mm"),
                hasSufficientMaterials,
                materialRequirements,
                availableProductionLine = productionLine != null ? new
                {
                    productionLine.Id,
                    productionLine.Name,
                    productionLine.EfficiencyFactor
                } : null
            });
        }

        private bool WorkOrderExists(int id)
        {
            return _context.WorkOrders.Any(e => e.Id == id);
        }
    }
}
