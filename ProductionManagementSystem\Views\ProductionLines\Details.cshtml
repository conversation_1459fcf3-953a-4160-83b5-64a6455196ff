@model ProductionManagementSystem.Models.ProductionLine
@{
    ViewData["Title"] = "Детали производственной линии";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="bi bi-diagram-3 me-2"></i>@Model.Name
    </h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
            <i class="bi bi-pencil me-1"></i>Редактировать
        </a>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-1"></i>Назад к списку
        </a>
    </div>
</div>

<!-- Основная информация -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header @(Model.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active ? "bg-success" : "bg-secondary") text-white">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>Основная информация
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Название:</dt>
                            <dd class="col-sm-8">@Model.Name</dd>

                            <dt class="col-sm-4">Статус:</dt>
                            <dd class="col-sm-8">
                                <span class="badge @(Model.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active ? "bg-success" : "bg-secondary")">
                                    @(Model.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active ? "Активна" : "Остановлена")
                                </span>
                            </dd>

                            <dt class="col-sm-4">Эффективность:</dt>
                            <dd class="col-sm-8">
                                <span class="fw-bold">@Model.EfficiencyFactor.ToString("F1")</span>
                                @if (Model.EfficiencyFactor > 1.0f)
                                {
                                    <small class="text-success">(повышенная)</small>
                                }
                                else if (Model.EfficiencyFactor < 1.0f)
                                {
                                    <small class="text-warning">(пониженная)</small>
                                }
                                else
                                {
                                    <small class="text-muted">(нормальная)</small>
                                }
                            </dd>

                            <dt class="col-sm-4">Доступность:</dt>
                            <dd class="col-sm-8">
                                @if (Model.IsAvailable)
                                {
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i>Доступна
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">
                                        <i class="bi bi-x-circle me-1"></i>Занята
                                    </span>
                                }
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <!-- Управление статусом -->
                        <div class="card border-light">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Управление</h6>
                            </div>
                            <div class="card-body">
                                @if (Model.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active)
                                {
                                    @if (Model.CurrentWorkOrder == null || Model.CurrentWorkOrder.Status != ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
                                    {
                                        <form asp-action="ToggleStatus" asp-route-id="@Model.Id" method="post" class="d-inline">
                                            <button type="submit" class="btn btn-warning btn-sm">
                                                <i class="bi bi-pause-circle me-1"></i>Остановить линию
                                            </button>
                                        </form>
                                    }
                                    else
                                    {
                                        <button class="btn btn-warning btn-sm" disabled>
                                            <i class="bi bi-pause-circle me-1"></i>Нельзя остановить (заказ в работе)
                                        </button>
                                    }
                                }
                                else
                                {
                                    <form asp-action="ToggleStatus" asp-route-id="@Model.Id" method="post" class="d-inline">
                                        <button type="submit" class="btn btn-success btn-sm">
                                            <i class="bi bi-play-circle me-1"></i>Запустить линию
                                        </button>
                                    </form>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Статистика -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>Статистика
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <div class="h4 text-success">
                            @(Model.WorkOrders?.Count(wo => wo.Status == ProductionManagementSystem.Models.WorkOrderStatus.Completed) ?? 0)
                        </div>
                        <small class="text-muted">Завершенных заказов</small>
                    </div>
                    <div class="col-6">
                        <div class="h5 text-warning">
                            @(Model.WorkOrders?.Count(wo => wo.Status == ProductionManagementSystem.Models.WorkOrderStatus.Pending) ?? 0)
                        </div>
                        <small class="text-muted">В очереди</small>
                    </div>
                    <div class="col-6">
                        <div class="h5 text-info">
                            @(Model.WorkOrders?.Count(wo => wo.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress) ?? 0)
                        </div>
                        <small class="text-muted">В работе</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Текущий заказ -->
@if (Model.CurrentWorkOrder != null)
{
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-clipboard-check me-2"></i>Текущий заказ
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Номер заказа:</dt>
                                <dd class="col-sm-8">
                                    <a asp-controller="WorkOrders" asp-action="Details" asp-route-id="@Model.CurrentWorkOrder.Id" class="text-decoration-none">
                                        #@Model.CurrentWorkOrder.Id
                                    </a>
                                </dd>

                                <dt class="col-sm-4">Продукт:</dt>
                                <dd class="col-sm-8">@Model.CurrentWorkOrder.Product.Name</dd>

                                <dt class="col-sm-4">Количество:</dt>
                                <dd class="col-sm-8">@Model.CurrentWorkOrder.Quantity</dd>

                                <dt class="col-sm-4">Статус:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge @(Model.CurrentWorkOrder.Status switch {
                                        ProductionManagementSystem.Models.WorkOrderStatus.Pending => "bg-warning",
                                        ProductionManagementSystem.Models.WorkOrderStatus.InProgress => "bg-info",
                                        ProductionManagementSystem.Models.WorkOrderStatus.Completed => "bg-success",
                                        ProductionManagementSystem.Models.WorkOrderStatus.Cancelled => "bg-danger",
                                        _ => "bg-secondary"
                                    })">
                                        @Model.CurrentWorkOrder.Status switch {
                                            ProductionManagementSystem.Models.WorkOrderStatus.Pending => "Ожидание",
                                            ProductionManagementSystem.Models.WorkOrderStatus.InProgress => "В работе",
                                            ProductionManagementSystem.Models.WorkOrderStatus.Completed => "Завершен",
                                            ProductionManagementSystem.Models.WorkOrderStatus.Cancelled => "Отменен",
                                            _ => "Неизвестно"
                                        }
                                    </span>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Дата начала:</dt>
                                <dd class="col-sm-8">@Model.CurrentWorkOrder.StartDate.ToString("dd.MM.yyyy HH:mm")</dd>

                                <dt class="col-sm-4">Плановое завершение:</dt>
                                <dd class="col-sm-8">@Model.CurrentWorkOrder.EstimatedEndDate.ToString("dd.MM.yyyy HH:mm")</dd>

                                @if (Model.CurrentWorkOrder.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
                                {
                                    <dt class="col-sm-4">Прогресс:</dt>
                                    <dd class="col-sm-8">
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-info" role="progressbar"
                                                 style="width: @Model.CurrentWorkOrder.Progress%"
                                                 aria-valuenow="@Model.CurrentWorkOrder.Progress"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                                @Model.CurrentWorkOrder.Progress%
                                            </div>
                                        </div>
                                    </dd>
                                }

                                @if (Model.CurrentWorkOrder.IsOverdue)
                                {
                                    <dt class="col-sm-4">Статус:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge bg-danger">
                                            <i class="bi bi-exclamation-triangle me-1"></i>Просрочен
                                        </span>
                                    </dd>
                                }
                            </dl>
                        </div>
                    </div>

                    @if (Model.CurrentWorkOrder.Status == ProductionManagementSystem.Models.WorkOrderStatus.Pending)
                    {
                        <div class="mt-3">
                            <form asp-action="ReleaseOrder" asp-route-id="@Model.Id" method="post" class="d-inline">
                                <button type="submit" class="btn btn-warning btn-sm">
                                    <i class="bi bi-x-circle me-1"></i>Снять заказ с линии
                                </button>
                            </form>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}
else if (Model.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active)
{
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-light">
                <div class="card-body text-center py-5">
                    <i class="bi bi-inbox display-4 text-muted"></i>
                    <h4 class="text-muted mt-3">Нет активного заказа</h4>
                    <p class="text-muted">Производственная линия готова к работе</p>
                    <button type="button" class="btn btn-primary" onclick="showAssignOrderModal(@Model.Id, '@Model.Name')">
                        <i class="bi bi-plus-circle me-1"></i>Назначить заказ
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<!-- История заказов -->
@if (Model.WorkOrders != null && Model.WorkOrders.Any())
{
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>История заказов
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>№</th>
                                    <th>Продукт</th>
                                    <th>Количество</th>
                                    <th>Дата начала</th>
                                    <th>Плановое завершение</th>
                                    <th>Статус</th>
                                    <th>Прогресс</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var order in Model.WorkOrders.OrderByDescending(wo => wo.Id))
                                {
                                    <tr class="@(order.IsOverdue && order.Status != ProductionManagementSystem.Models.WorkOrderStatus.Completed ? "table-danger" : "")">
                                        <td>
                                            <a asp-controller="WorkOrders" asp-action="Details" asp-route-id="@order.Id" class="text-decoration-none">
                                                #@order.Id
                                            </a>
                                        </td>
                                        <td>@order.Product.Name</td>
                                        <td>@order.Quantity</td>
                                        <td>@order.StartDate.ToString("dd.MM.yyyy HH:mm")</td>
                                        <td>@order.EstimatedEndDate.ToString("dd.MM.yyyy HH:mm")</td>
                                        <td>
                                            <span class="badge @(order.Status switch {
                                                ProductionManagementSystem.Models.WorkOrderStatus.Pending => "bg-warning",
                                                ProductionManagementSystem.Models.WorkOrderStatus.InProgress => "bg-info",
                                                ProductionManagementSystem.Models.WorkOrderStatus.Completed => "bg-success",
                                                ProductionManagementSystem.Models.WorkOrderStatus.Cancelled => "bg-danger",
                                                _ => "bg-secondary"
                                            })">
                                                @order.Status switch {
                                                    ProductionManagementSystem.Models.WorkOrderStatus.Pending => "Ожидание",
                                                    ProductionManagementSystem.Models.WorkOrderStatus.InProgress => "В работе",
                                                    ProductionManagementSystem.Models.WorkOrderStatus.Completed => "Завершен",
                                                    ProductionManagementSystem.Models.WorkOrderStatus.Cancelled => "Отменен",
                                                    _ => "Неизвестно"
                                                }
                                            </span>
                                        </td>
                                        <td>
                                            @if (order.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
                                            {
                                                <div class="progress" style="width: 80px; height: 20px;">
                                                    <div class="progress-bar bg-info" role="progressbar"
                                                         style="width: @order.Progress%"
                                                         aria-valuenow="@order.Progress"
                                                         aria-valuemin="0"
                                                         aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <small>@order.Progress%</small>
                                            }
                                            else if (order.Status == ProductionManagementSystem.Models.WorkOrderStatus.Completed)
                                            {
                                                <span class="text-success">
                                                    <i class="bi bi-check-circle"></i> 100%
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <a asp-controller="WorkOrders" asp-action="Details" asp-route-id="@order.Id"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<!-- Модальное окно для назначения заказа -->
<div class="modal fade" id="assignOrderModal" tabindex="-1" aria-labelledby="assignOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignOrderModalLabel">Назначить заказ на линию</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="assignOrderContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Загрузка...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                <button type="button" class="btn btn-primary" id="assignOrderBtn" onclick="assignOrder()" disabled>
                    Назначить заказ
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let selectedLineId = null;
        let selectedOrderId = null;

        // Показать модальное окно назначения заказа
        async function showAssignOrderModal(lineId, lineName) {
            selectedLineId = lineId;
            document.getElementById('assignOrderModalLabel').textContent = `Назначить заказ на линию "${lineName}"`;

            const modal = new bootstrap.Modal(document.getElementById('assignOrderModal'));
            modal.show();

            // Загружаем доступные заказы
            try {
                const response = await fetch('/ProductionLines/GetAvailableOrders');
                if (response.ok) {
                    const orders = await response.json();
                    displayAvailableOrders(orders);
                } else {
                    document.getElementById('assignOrderContent').innerHTML =
                        '<div class="alert alert-danger">Ошибка при загрузке заказов</div>';
                }
            } catch (error) {
                console.error('Ошибка:', error);
                document.getElementById('assignOrderContent').innerHTML =
                    '<div class="alert alert-danger">Ошибка при загрузке заказов</div>';
            }
        }

        // Отображение доступных заказов
        function displayAvailableOrders(orders) {
            const content = document.getElementById('assignOrderContent');

            if (orders.length === 0) {
                content.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Нет доступных заказов для назначения
                    </div>
                `;
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="50"></th>
                                <th>№</th>
                                <th>Продукт</th>
                                <th>Количество</th>
                                <th>Дата начала</th>
                                <th>Плановое завершение</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            orders.forEach(order => {
                html += `
                    <tr class="order-row" data-order-id="${order.id}" onclick="selectOrder(${order.id})">
                        <td>
                            <input type="radio" name="selectedOrder" value="${order.id}" class="form-check-input">
                        </td>
                        <td>#${order.id}</td>
                        <td>${order.productName}</td>
                        <td>${order.quantity}</td>
                        <td>${order.startDate}</td>
                        <td>${order.estimatedEndDate}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            content.innerHTML = html;
        }

        // Выбор заказа
        function selectOrder(orderId) {
            selectedOrderId = orderId;

            // Снимаем выделение со всех строк
            document.querySelectorAll('.order-row').forEach(row => {
                row.classList.remove('table-primary');
            });

            // Выделяем выбранную строку
            const selectedRow = document.querySelector(`[data-order-id="${orderId}"]`);
            selectedRow.classList.add('table-primary');

            // Отмечаем радиокнопку
            const radio = selectedRow.querySelector('input[type="radio"]');
            radio.checked = true;

            // Включаем кнопку назначения
            document.getElementById('assignOrderBtn').disabled = false;
        }

        // Назначить заказ
        async function assignOrder() {
            if (!selectedLineId || !selectedOrderId) {
                alert('Выберите заказ для назначения');
                return;
            }

            try {
                const response = await fetch('/ProductionLines/AssignOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `lineId=${selectedLineId}&orderId=${selectedOrderId}&__RequestVerificationToken=${getAntiForgeryToken()}`
                });

                if (response.ok) {
                    bootstrap.Modal.getInstance(document.getElementById('assignOrderModal')).hide();
                    location.reload();
                } else {
                    alert('Ошибка при назначении заказа');
                }
            } catch (error) {
                console.error('Ошибка:', error);
                alert('Ошибка при назначении заказа');
            }
        }

        // Получение CSRF токена
        function getAntiForgeryToken() {
            const token = document.querySelector('input[name="__RequestVerificationToken"]');
            return token ? token.value : '';
        }
    </script>
}