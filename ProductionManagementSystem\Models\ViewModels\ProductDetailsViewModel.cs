namespace ProductionManagementSystem.Models.ViewModels
{
    public class ProductDetailsViewModel
    {
        public Product Product { get; set; } = null!;
        public List<MaterialRequirement> Materials { get; set; } = new();
        public List<WorkOrder> RecentWorkOrders { get; set; } = new();
        public int TotalProduced { get; set; }
        public int PendingOrders { get; set; }
    }

    public class MaterialRequirementDetail
    {
        public Material Material { get; set; } = null!;
        public decimal QuantityNeeded { get; set; }
        public decimal TotalCost { get; set; }
    }
}
