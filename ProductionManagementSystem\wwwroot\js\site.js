﻿// Система управления производством - JavaScript функции

// Глобальные переменные
let productionCalculationTimeout;

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', function() {
    initializeTooltips();
    initializeProgressBars();
    initializeAutoRefresh();
});

// Инициализация всплывающих подсказок Bootstrap
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Инициализация прогресс-баров
function initializeProgressBars() {
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const value = bar.getAttribute('aria-valuenow');
        bar.style.width = value + '%';
    });
}

// Автоматическое обновление данных
function initializeAutoRefresh() {
    // Обновляем статистику каждые 30 секунд
    setInterval(refreshDashboardStats, 30000);

    // Обновляем статусы производственных линий каждые 10 секунд
    setInterval(refreshProductionLineStatus, 10000);
}

// Обновление статистики на главной странице
async function refreshDashboardStats() {
    try {
        const response = await fetch('/api/dashboard/stats');
        if (response.ok) {
            const data = await response.json();
            updateDashboardCards(data);
        }
    } catch (error) {
        console.error('Ошибка при обновлении статистики:', error);
    }
}

// Обновление карточек на главной странице
function updateDashboardCards(data) {
    const cards = {
        'total-products': data.totalProducts,
        'total-materials': data.totalMaterials,
        'active-lines': data.activeProductionLines,
        'in-progress-orders': data.inProgressWorkOrders
    };

    Object.keys(cards).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = cards[id];
        }
    });
}

// Обновление статусов производственных линий
async function refreshProductionLineStatus() {
    const statusElements = document.querySelectorAll('[data-production-line-id]');

    for (const element of statusElements) {
        const lineId = element.getAttribute('data-production-line-id');
        try {
            const response = await fetch(`/api/lines/${lineId}`);
            if (response.ok) {
                const data = await response.json();
                updateProductionLineStatus(element, data);
            }
        } catch (error) {
            console.error(`Ошибка при обновлении статуса линии ${lineId}:`, error);
        }
    }
}

// Обновление статуса конкретной производственной линии
function updateProductionLineStatus(element, data) {
    const statusBadge = element.querySelector('.status-badge');
    if (statusBadge) {
        statusBadge.className = `badge ${data.status === 'Active' ? 'bg-success' : 'bg-secondary'}`;
        statusBadge.textContent = data.status === 'Active' ? 'Активна' : 'Остановлена';
    }
}

// Расчет производственного времени
async function calculateProductionTime(productId, quantity, startDate) {
    if (!productId || !quantity || quantity <= 0) {
        return;
    }

    clearTimeout(productionCalculationTimeout);
    productionCalculationTimeout = setTimeout(async () => {
        try {
            const response = await fetch('/WorkOrders/Calculate?' + new URLSearchParams({
                productId: productId,
                quantity: quantity,
                startDate: startDate
            }));

            if (response.ok) {
                const data = await response.json();
                displayCalculationResults(data);
            }
        } catch (error) {
            console.error('Ошибка при расчете времени производства:', error);
        }
    }, 500);
}

// Отображение результатов расчета
function displayCalculationResults(data) {
    const resultsContainer = document.getElementById('calculation-results');
    if (!resultsContainer) return;

    resultsContainer.innerHTML = `
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Результаты расчета</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Время производства:</strong><br>
                        ${data.productionTimeHours} часов (${data.productionTimeMinutes} минут)
                    </div>
                    <div class="col-md-6">
                        <strong>Ожидаемая дата завершения:</strong><br>
                        ${new Date(data.estimatedEndDate).toLocaleString('ru-RU')}
                    </div>
                </div>
                <div class="mt-3">
                    <div class="alert ${data.hasSufficientMaterials ? 'alert-success' : 'alert-warning'}">
                        <i class="bi ${data.hasSufficientMaterials ? 'bi-check-circle' : 'bi-exclamation-triangle'} me-2"></i>
                        ${data.hasSufficientMaterials ? 'Все материалы в наличии' : 'Недостаточно материалов'}
                    </div>
                </div>
                ${data.availableProductionLine ? `
                    <div class="mt-2">
                        <strong>Рекомендуемая линия:</strong> ${data.availableProductionLine.name}
                        (эффективность: ${data.availableProductionLine.efficiencyFactor})
                    </div>
                ` : '<div class="alert alert-info mt-2">Нет доступных производственных линий</div>'}
            </div>
        </div>
    `;
}

// Обновление прогресса заказа
async function updateOrderProgress(orderId, progress) {
    try {
        const response = await fetch(`/WorkOrders/UpdateProgress/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `progress=${progress}&__RequestVerificationToken=${getAntiForgeryToken()}`
        });

        if (response.ok) {
            showNotification('Прогресс заказа обновлен', 'success');
            // Обновляем прогресс-бар на странице
            const progressBar = document.querySelector(`[data-order-id="${orderId}"] .progress-bar`);
            if (progressBar) {
                progressBar.style.width = progress + '%';
                progressBar.setAttribute('aria-valuenow', progress);
                progressBar.textContent = progress + '%';
            }
        } else {
            showNotification('Ошибка при обновлении прогресса', 'error');
        }
    } catch (error) {
        console.error('Ошибка при обновлении прогресса:', error);
        showNotification('Ошибка при обновлении прогресса', 'error');
    }
}

// Изменение статуса производственной линии
async function toggleProductionLineStatus(lineId, currentStatus) {
    const newStatus = currentStatus === 'Active' ? 'Stopped' : 'Active';

    try {
        const response = await fetch(`/api/lines/${lineId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: newStatus })
        });

        if (response.ok) {
            showNotification('Статус производственной линии изменен', 'success');
            // Перезагружаем страницу для обновления данных
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Ошибка при изменении статуса', 'error');
        }
    } catch (error) {
        console.error('Ошибка при изменении статуса линии:', error);
        showNotification('Ошибка при изменении статуса', 'error');
    }
}

// Быстрое пополнение материала
async function quickReplenishMaterial(materialId, quantity) {
    try {
        const response = await fetch(`/api/materials/${materialId}/stock`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ quantity: quantity })
        });

        if (response.ok) {
            showNotification('Запас материала пополнен', 'success');
            // Обновляем количество на странице
            const quantityElement = document.querySelector(`[data-material-id="${materialId}"] .material-quantity`);
            if (quantityElement) {
                quantityElement.textContent = quantity.toFixed(2);
            }
        } else {
            showNotification('Ошибка при пополнении запаса', 'error');
        }
    } catch (error) {
        console.error('Ошибка при пополнении материала:', error);
        showNotification('Ошибка при пополнении запаса', 'error');
    }
}

// Показ уведомлений
function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const icon = {
        'success': 'bi-check-circle',
        'error': 'bi-exclamation-triangle',
        'warning': 'bi-exclamation-circle',
        'info': 'bi-info-circle'
    }[type] || 'bi-info-circle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            <i class="${icon} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', alertHtml);

    // Автоматически скрываем уведомление через 5 секунд
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert.position-fixed');
        alerts.forEach(alert => {
            if (alert.textContent.includes(message)) {
                alert.remove();
            }
        });
    }, 5000);
}

// Получение CSRF токена
function getAntiForgeryToken() {
    const token = document.querySelector('input[name="__RequestVerificationToken"]');
    return token ? token.value : '';
}

// Форматирование чисел
function formatNumber(number, decimals = 2) {
    return new Intl.NumberFormat('ru-RU', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

// Форматирование даты
function formatDate(date) {
    return new Intl.DateTimeFormat('ru-RU', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

// Подтверждение действий
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// Экспорт функций для использования в других скриптах
window.ProductionManagement = {
    calculateProductionTime,
    updateOrderProgress,
    toggleProductionLineStatus,
    quickReplenishMaterial,
    showNotification,
    formatNumber,
    formatDate,
    confirmAction
};
