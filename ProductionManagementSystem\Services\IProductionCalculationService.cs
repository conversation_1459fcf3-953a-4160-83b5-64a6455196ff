using ProductionManagementSystem.Models;
using ProductionManagementSystem.Models.ViewModels;

namespace ProductionManagementSystem.Services
{
    public interface IProductionCalculationService
    {
        /// <summary>
        /// Рассчитывает время производства заказа
        /// </summary>
        int CalculateProductionTime(int productId, int quantity, float efficiencyFactor = 1.0f);

        /// <summary>
        /// Рассчитывает дату окончания производства
        /// </summary>
        DateTime CalculateEstimatedEndDate(DateTime startDate, int productionTimeMinutes);

        /// <summary>
        /// Проверяет наличие достаточного количества материалов для производства
        /// </summary>
        Task<bool> CheckMaterialAvailabilityAsync(int productId, int quantity);

        /// <summary>
        /// Получает требования к материалам для производства
        /// </summary>
        Task<List<MaterialRequirement>> GetMaterialRequirementsAsync(int productId, int quantity);

        /// <summary>
        /// Находит доступную производственную линию для продукта
        /// </summary>
        Task<ProductionLine?> FindAvailableProductionLineAsync();

        /// <summary>
        /// Резервирует материалы для производственного заказа
        /// </summary>
        Task<bool> ReserveMaterialsAsync(int productId, int quantity);

        /// <summary>
        /// Освобождает зарезервированные материалы
        /// </summary>
        Task ReleaseMaterialsAsync(int productId, int quantity);
    }
}
