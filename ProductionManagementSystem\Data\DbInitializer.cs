using ProductionManagementSystem.Models;

namespace ProductionManagementSystem.Data
{
    public static class DbInitializer
    {
        public static void Initialize(ApplicationDbContext context)
        {
            context.Database.EnsureCreated();

            // Проверяем, есть ли уже данные
            if (context.Products.Any())
            {
                return; // База данных уже заполнена
            }

            // Создаем материалы
            var materials = new Material[]
            {
                new Material { Name = "Сталь листовая", Quantity = 1000, UnitOfMeasure = "кг", MinimalStock = 100 },
                new Material { Name = "Алюминий", Quantity = 500, UnitOfMeasure = "кг", MinimalStock = 50 },
                new Material { Name = "Пластик ABS", Quantity = 200, UnitOfMeasure = "кг", MinimalStock = 30 },
                new Material { Name = "Болты M8", Quantity = 5000, UnitOfMeasure = "шт", MinimalStock = 500 },
                new Material { Name = "Краска", Quantity = 100, UnitOfMeasure = "л", MinimalStock = 20 },
                new Material { Name = "Резина", Quantity = 150, UnitOfMeasure = "кг", MinimalStock = 25 },
                new Material { Name = "Стекло", Quantity = 80, UnitOfMeasure = "м²", MinimalStock = 10 }
            };

            context.Materials.AddRange(materials);
            context.SaveChanges();

            // Создаем продукты
            var products = new Product[]
            {
                new Product 
                { 
                    Name = "Автомобильная дверь", 
                    Description = "Передняя левая дверь для легкового автомобиля",
                    Category = "Автозапчасти", 
                    MinimalStock = 10, 
                    ProductionTimePerUnit = 120,
                    Specifications = "{\"weight\": \"15kg\", \"material\": \"steel\", \"color\": \"primer\"}"
                },
                new Product 
                { 
                    Name = "Пластиковый корпус", 
                    Description = "Корпус для электронного устройства",
                    Category = "Электроника", 
                    MinimalStock = 50, 
                    ProductionTimePerUnit = 30,
                    Specifications = "{\"dimensions\": \"200x150x50mm\", \"material\": \"ABS\"}"
                },
                new Product 
                { 
                    Name = "Алюминиевый радиатор", 
                    Description = "Радиатор охлаждения",
                    Category = "Охлаждение", 
                    MinimalStock = 20, 
                    ProductionTimePerUnit = 90,
                    Specifications = "{\"fins\": 40, \"material\": \"aluminum\", \"capacity\": \"2L\"}"
                }
            };

            context.Products.AddRange(products);
            context.SaveChanges();

            // Создаем производственные линии
            var productionLines = new ProductionLine[]
            {
                new ProductionLine { Name = "Линия штамповки", Status = ProductionLineStatus.Active, EfficiencyFactor = 1.2f },
                new ProductionLine { Name = "Линия литья пластика", Status = ProductionLineStatus.Active, EfficiencyFactor = 1.0f },
                new ProductionLine { Name = "Линия обработки металла", Status = ProductionLineStatus.Stopped, EfficiencyFactor = 0.9f },
                new ProductionLine { Name = "Сборочная линия", Status = ProductionLineStatus.Active, EfficiencyFactor = 1.1f }
            };

            context.ProductionLines.AddRange(productionLines);
            context.SaveChanges();

            // Создаем связи продукт-материал
            var productMaterials = new ProductMaterial[]
            {
                // Автомобильная дверь
                new ProductMaterial { ProductId = 1, MaterialId = 1, QuantityNeeded = 15 }, // Сталь
                new ProductMaterial { ProductId = 1, MaterialId = 4, QuantityNeeded = 8 },  // Болты
                new ProductMaterial { ProductId = 1, MaterialId = 5, QuantityNeeded = 0.5m }, // Краска
                
                // Пластиковый корпус
                new ProductMaterial { ProductId = 2, MaterialId = 3, QuantityNeeded = 0.8m }, // Пластик ABS
                
                // Алюминиевый радиатор
                new ProductMaterial { ProductId = 3, MaterialId = 2, QuantityNeeded = 5 },   // Алюминий
                new ProductMaterial { ProductId = 3, MaterialId = 6, QuantityNeeded = 0.2m }  // Резина
            };

            context.ProductMaterials.AddRange(productMaterials);
            context.SaveChanges();

            // Создаем рабочие заказы
            var workOrders = new WorkOrder[]
            {
                new WorkOrder 
                { 
                    ProductId = 1, 
                    ProductionLineId = 1, 
                    Quantity = 10, 
                    StartDate = DateTime.Now.AddDays(-2), 
                    EstimatedEndDate = DateTime.Now.AddDays(1),
                    Status = WorkOrderStatus.InProgress,
                    Progress = 60
                },
                new WorkOrder 
                { 
                    ProductId = 2, 
                    ProductionLineId = 2, 
                    Quantity = 100, 
                    StartDate = DateTime.Now, 
                    EstimatedEndDate = DateTime.Now.AddDays(2),
                    Status = WorkOrderStatus.Pending
                },
                new WorkOrder 
                { 
                    ProductId = 3, 
                    Quantity = 5, 
                    StartDate = DateTime.Now.AddDays(1), 
                    EstimatedEndDate = DateTime.Now.AddDays(3),
                    Status = WorkOrderStatus.Pending
                }
            };

            context.WorkOrders.AddRange(workOrders);
            context.SaveChanges();
        }
    }
}
