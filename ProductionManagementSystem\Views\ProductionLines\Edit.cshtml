@model ProductionManagementSystem.Models.ProductionLine
@{
    ViewData["Title"] = "Редактировать производственную линию";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="bi bi-pencil me-2"></i>Редактировать производственную линию
    </h1>
    <div>
        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">
            <i class="bi bi-eye me-1"></i>Просмотр
        </a>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-1"></i>Назад к списку
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>Редактирование: @Model.Name
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="Edit" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <input type="hidden" asp-for="Id" />
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Name" class="form-label">Название линии</label>
                                <input asp-for="Name" class="form-control" placeholder="Введите название производственной линии" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Status" class="form-label">Статус</label>
                                <select asp-for="Status" class="form-select">
                                    <option value="@ProductionManagementSystem.Models.ProductionLineStatus.Active">Активна</option>
                                    <option value="@ProductionManagementSystem.Models.ProductionLineStatus.Stopped">Остановлена</option>
                                </select>
                                <span asp-validation-for="Status" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="EfficiencyFactor" class="form-label">Коэффициент эффективности</label>
                                <input asp-for="EfficiencyFactor" class="form-control" type="number" step="0.1" min="0.1" max="3.0" />
                                <div class="form-text">
                                    Значение от 0.1 до 3.0, где 1.0 - нормальная эффективность
                                </div>
                                <span asp-validation-for="EfficiencyFactor" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Текущее состояние</label>
                                <div class="card border-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">ID линии:</small><br>
                                                <strong>#@Model.Id</strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Доступность:</small><br>
                                                @if (Model.IsAvailable)
                                                {
                                                    <span class="badge bg-success">Доступна</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Занята</span>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    @if (Model.CurrentWorkOrderId.HasValue)
                    {
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle me-2"></i>Внимание!
                            </h6>
                            <p class="mb-0">
                                На данной линии выполняется заказ #@Model.CurrentWorkOrderId. 
                                Изменение статуса может повлиять на выполнение заказа.
                            </p>
                        </div>
                    }
                    
                    <div class="mb-4">
                        <h6>Описание коэффициентов эффективности:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <div class="h5 text-warning">0.1 - 0.9</div>
                                        <small class="text-muted">Пониженная эффективность</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <div class="h5 text-success">1.0</div>
                                        <small class="text-muted">Нормальная эффективность</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <div class="h5 text-info">1.1 - 3.0</div>
                                        <small class="text-muted">Повышенная эффективность</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-1"></i>Отмена
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-check-circle me-1"></i>Сохранить изменения
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Динамическое обновление описания эффективности
        document.getElementById('EfficiencyFactor').addEventListener('input', function() {
            const value = parseFloat(this.value);
            const cards = document.querySelectorAll('.card.border-warning, .card.border-success, .card.border-info');
            
            // Сбрасываем все выделения
            cards.forEach(card => {
                card.classList.remove('bg-light');
            });
            
            // Выделяем соответствующую карточку
            if (value < 1.0) {
                document.querySelector('.card.border-warning').classList.add('bg-light');
            } else if (value === 1.0) {
                document.querySelector('.card.border-success').classList.add('bg-light');
            } else {
                document.querySelector('.card.border-info').classList.add('bg-light');
            }
        });
        
        // Инициализация при загрузке
        document.addEventListener('DOMContentLoaded', function() {
            const efficiencyInput = document.getElementById('EfficiencyFactor');
            if (efficiencyInput) {
                efficiencyInput.dispatchEvent(new Event('input'));
            }
        });
    </script>
}
