using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Models;

namespace ProductionManagementSystem.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Product> Products { get; set; }
        public DbSet<Material> Materials { get; set; }
        public DbSet<ProductionLine> ProductionLines { get; set; }
        public DbSet<ProductMaterial> ProductMaterials { get; set; }
        public DbSet<WorkOrder> WorkOrders { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Конфигурация ProductMaterial (многие-ко-многим)
            modelBuilder.Entity<ProductMaterial>()
                .HasKey(pm => new { pm.ProductId, pm.MaterialId });

            modelBuilder.Entity<ProductMaterial>()
                .HasOne(pm => pm.Product)
                .WithMany(p => p.ProductMaterials)
                .HasForeignKey(pm => pm.ProductId);

            modelBuilder.Entity<ProductMaterial>()
                .HasOne(pm => pm.Material)
                .WithMany(m => m.ProductMaterials)
                .HasForeignKey(pm => pm.MaterialId);

            // Конфигурация WorkOrder
            modelBuilder.Entity<WorkOrder>()
                .HasOne(wo => wo.Product)
                .WithMany(p => p.WorkOrders)
                .HasForeignKey(wo => wo.ProductId);

            modelBuilder.Entity<WorkOrder>()
                .HasOne(wo => wo.ProductionLine)
                .WithMany(pl => pl.WorkOrders)
                .HasForeignKey(wo => wo.ProductionLineId)
                .OnDelete(DeleteBehavior.SetNull);

            // Конфигурация ProductionLine
            modelBuilder.Entity<ProductionLine>()
                .HasOne(pl => pl.CurrentWorkOrder)
                .WithOne()
                .HasForeignKey<ProductionLine>(pl => pl.CurrentWorkOrderId)
                .OnDelete(DeleteBehavior.SetNull);

            // Конфигурация типов данных
            modelBuilder.Entity<Material>()
                .Property(m => m.Quantity)
                .HasColumnType("decimal(18,2)");

            modelBuilder.Entity<Material>()
                .Property(m => m.MinimalStock)
                .HasColumnType("decimal(18,2)");

            modelBuilder.Entity<ProductMaterial>()
                .Property(pm => pm.QuantityNeeded)
                .HasColumnType("decimal(18,2)");

            // Конфигурация DateTime полей для PostgreSQL
            modelBuilder.Entity<WorkOrder>()
                .Property(wo => wo.StartDate)
                .HasColumnType("timestamp without time zone");

            modelBuilder.Entity<WorkOrder>()
                .Property(wo => wo.EstimatedEndDate)
                .HasColumnType("timestamp without time zone");

            modelBuilder.Entity<WorkOrder>()
                .Property(wo => wo.ActualStartDate)
                .HasColumnType("timestamp without time zone");

            modelBuilder.Entity<WorkOrder>()
                .Property(wo => wo.ActualEndDate)
                .HasColumnType("timestamp without time zone");

            // Индексы для производительности
            modelBuilder.Entity<WorkOrder>()
                .HasIndex(wo => wo.Status);

            modelBuilder.Entity<WorkOrder>()
                .HasIndex(wo => wo.StartDate);

            modelBuilder.Entity<Material>()
                .HasIndex(m => m.Name);

            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Category);
        }
    }
}
