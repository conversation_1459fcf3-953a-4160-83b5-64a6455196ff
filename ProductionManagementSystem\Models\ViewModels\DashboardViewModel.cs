namespace ProductionManagementSystem.Models.ViewModels
{
    public class DashboardViewModel
    {
        public int TotalProducts { get; set; }
        public int TotalMaterials { get; set; }
        public int ActiveProductionLines { get; set; }
        public int PendingWorkOrders { get; set; }
        public int InProgressWorkOrders { get; set; }
        public int OverdueWorkOrders { get; set; }
        public int LowStockMaterials { get; set; }

        public List<WorkOrder> RecentWorkOrders { get; set; } = new();
        public List<Material> LowStockMaterialsList { get; set; } = new();
        public List<ProductionLine> ProductionLines { get; set; } = new();

        // Статистика производства
        public int TotalCompletedToday { get; set; }
        public int TotalCompletedThisWeek { get; set; }
        public int TotalCompletedThisMonth { get; set; }
    }
}
