using System.ComponentModel.DataAnnotations;

namespace ProductionManagementSystem.Models
{
    public class Product
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        public string? Specifications { get; set; } // JSON format

        [Required]
        [StringLength(100)]
        public string Category { get; set; } = string.Empty;

        [Range(0, int.MaxValue)]
        public int MinimalStock { get; set; }

        [Range(1, int.MaxValue)]
        public int ProductionTimePerUnit { get; set; } // в минутах

        // Navigation properties
        public virtual ICollection<ProductMaterial> ProductMaterials { get; set; } = new List<ProductMaterial>();
        public virtual ICollection<WorkOrder> WorkOrders { get; set; } = new List<WorkOrder>();
    }
}
