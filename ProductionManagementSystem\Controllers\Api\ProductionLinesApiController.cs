using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;

namespace ProductionManagementSystem.Controllers.Api
{
    [Route("api/lines")]
    [ApiController]
    public class ProductionLinesApiController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public ProductionLinesApiController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/lines?available=true
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProductionLine>>> GetProductionLines(bool? available = null)
        {
            var query = _context.ProductionLines
                .Include(pl => pl.CurrentWorkOrder)
                .ThenInclude(wo => wo!.Product)
                .AsQueryable();

            if (available == true)
            {
                query = query.Where(pl => pl.Status == ProductionLineStatus.Active && pl.CurrentWorkOrderId == null);
            }

            var productionLines = await query
                .OrderBy(pl => pl.Name)
                .ToListAsync();

            return Ok(productionLines);
        }

        // GET: api/lines/5/schedule
        [HttpGet("{id}/schedule")]
        public async Task<ActionResult<object>> GetProductionLineSchedule(int id)
        {
            var productionLine = await _context.ProductionLines
                .Include(pl => pl.CurrentWorkOrder)
                .ThenInclude(wo => wo!.Product)
                .FirstOrDefaultAsync(pl => pl.Id == id);

            if (productionLine == null)
            {
                return NotFound();
            }

            // Получаем все заказы для этой линии
            var workOrders = await _context.WorkOrders
                .Include(wo => wo.Product)
                .Where(wo => wo.ProductionLineId == id)
                .OrderBy(wo => wo.StartDate)
                .ToListAsync();

            // Получаем запланированные заказы (без назначенной линии)
            var pendingOrders = await _context.WorkOrders
                .Include(wo => wo.Product)
                .Where(wo => wo.ProductionLineId == null && wo.Status == WorkOrderStatus.Pending)
                .OrderBy(wo => wo.StartDate)
                .Take(10) // Показываем только первые 10
                .ToListAsync();

            var result = new
            {
                ProductionLine = productionLine,
                AssignedOrders = workOrders,
                PendingOrders = pendingOrders,
                IsAvailable = productionLine.IsAvailable,
                Utilization = CalculateUtilization(workOrders)
            };

            return Ok(result);
        }

        // PUT: api/lines/5/status
        [HttpPut("{id}/status")]
        public async Task<IActionResult> UpdateStatus(int id, [FromBody] UpdateLineStatusRequest request)
        {
            var productionLine = await _context.ProductionLines
                .Include(pl => pl.CurrentWorkOrder)
                .FirstOrDefaultAsync(pl => pl.Id == id);

            if (productionLine == null)
            {
                return NotFound();
            }

            // Проверяем, можно ли остановить линию
            if (request.Status == ProductionLineStatus.Stopped && 
                productionLine.CurrentWorkOrder != null && 
                productionLine.CurrentWorkOrder.Status == WorkOrderStatus.InProgress)
            {
                return BadRequest("Нельзя остановить линию с активным заказом в процессе выполнения");
            }

            productionLine.Status = request.Status;
            await _context.SaveChangesAsync();

            return Ok(new 
            { 
                productionLine.Id, 
                productionLine.Name, 
                productionLine.Status, 
                IsAvailable = productionLine.IsAvailable 
            });
        }

        // PUT: api/lines/5/efficiency
        [HttpPut("{id}/efficiency")]
        public async Task<IActionResult> UpdateEfficiency(int id, [FromBody] UpdateEfficiencyRequest request)
        {
            var productionLine = await _context.ProductionLines.FindAsync(id);
            if (productionLine == null)
            {
                return NotFound();
            }

            if (request.EfficiencyFactor < 0.5f || request.EfficiencyFactor > 2.0f)
            {
                return BadRequest("Коэффициент эффективности должен быть от 0.5 до 2.0");
            }

            productionLine.EfficiencyFactor = request.EfficiencyFactor;
            await _context.SaveChangesAsync();

            return Ok(new { productionLine.Id, productionLine.EfficiencyFactor });
        }

        // POST: api/lines/5/assign
        [HttpPost("{id}/assign")]
        public async Task<IActionResult> AssignWorkOrder(int id, [FromBody] AssignWorkOrderRequest request)
        {
            var productionLine = await _context.ProductionLines.FindAsync(id);
            if (productionLine == null)
            {
                return NotFound("Производственная линия не найдена");
            }

            if (!productionLine.IsAvailable)
            {
                return BadRequest("Производственная линия недоступна");
            }

            var workOrder = await _context.WorkOrders.FindAsync(request.WorkOrderId);
            if (workOrder == null)
            {
                return NotFound("Заказ не найден");
            }

            if (workOrder.Status != WorkOrderStatus.Pending)
            {
                return BadRequest("Можно назначить только заказы в статусе 'Ожидание'");
            }

            // Назначаем заказ на линию
            workOrder.ProductionLineId = id;
            productionLine.CurrentWorkOrderId = request.WorkOrderId;

            await _context.SaveChangesAsync();

            return Ok(new { Message = "Заказ успешно назначен на производственную линию" });
        }

        private double CalculateUtilization(List<WorkOrder> workOrders)
        {
            if (!workOrders.Any()) return 0;

            var now = DateTime.Now;
            var weekAgo = now.AddDays(-7);
            
            var recentOrders = workOrders
                .Where(wo => wo.StartDate >= weekAgo && wo.Status == WorkOrderStatus.Completed)
                .ToList();

            if (!recentOrders.Any()) return 0;

            var totalProductionTime = recentOrders.Sum(wo => wo.EstimatedDuration.TotalHours);
            var totalAvailableTime = 7 * 8; // 7 дней по 8 часов

            return Math.Min(100, (totalProductionTime / totalAvailableTime) * 100);
        }
    }

    public class UpdateLineStatusRequest
    {
        public ProductionLineStatus Status { get; set; }
    }

    public class UpdateEfficiencyRequest
    {
        public float EfficiencyFactor { get; set; }
    }

    public class AssignWorkOrderRequest
    {
        public int WorkOrderId { get; set; }
    }
}
