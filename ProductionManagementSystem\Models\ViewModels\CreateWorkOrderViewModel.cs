using System.ComponentModel.DataAnnotations;

namespace ProductionManagementSystem.Models.ViewModels
{
    public class CreateWorkOrderViewModel
    {
        [Required]
        [Display(Name = "Продукт")]
        public int ProductId { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "Количество должно быть больше 0")]
        [Display(Name = "Количество")]
        public int Quantity { get; set; }

        [Required]
        [Display(Name = "Дата начала")]
        public DateTime StartDate { get; set; } = DateTime.Now;

        [Display(Name = "Производственная линия")]
        public int? ProductionLineId { get; set; }

        // Для отображения в форме
        public List<Product> AvailableProducts { get; set; } = new();
        public List<ProductionLine> AvailableProductionLines { get; set; } = new();

        // Расчетные поля
        public int EstimatedProductionTimeMinutes { get; set; }
        public DateTime EstimatedEndDate { get; set; }
        public bool HasSufficientMaterials { get; set; }
        public List<MaterialRequirement> MaterialRequirements { get; set; } = new();
    }

    public class MaterialRequirement
    {
        public string MaterialName { get; set; } = string.Empty;
        public decimal RequiredQuantity { get; set; }
        public decimal AvailableQuantity { get; set; }
        public string UnitOfMeasure { get; set; } = string.Empty;
        public bool IsSufficient => AvailableQuantity >= RequiredQuantity;
    }
}
